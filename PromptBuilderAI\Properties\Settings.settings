﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile CurrentProfile="(Default)" GeneratedClassNamespace="PromptBuilderAI.Properties" GeneratedClassName="Settings" xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings">
  <Profiles />
  <Settings>
    <Setting Name="ApiKey" Type="System.String" Scope="User">
      <Value Profile="(Default)"></Value>
    </Setting>
    <Setting Name="AutoSavePrompts" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="AutoLoadModels" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="ShowNotifications" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="RememberWindowState" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="DefaultProjectType" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">2</Value>
    </Setting>
    <Setting Name="ThemeMode" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="PrimaryColor" Type="System.String" Scope="User">
      <Value Profile="(Default)">Deep Purple</Value>
    </Setting>
    <Setting Name="SelectedModel" Type="System.String" Scope="User">
      <Value Profile="(Default)">openai/gpt-4</Value>
    </Setting>
    <Setting Name="IsDarkMode" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="IsColorSelectionEnabled" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
  </Settings>
</SettingsFile>