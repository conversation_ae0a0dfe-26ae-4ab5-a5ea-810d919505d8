using System;
using System.Windows;
using System.Windows.Media.Animation;
using System.Windows.Threading;
using Microsoft.Extensions.DependencyInjection;
using PromptBuilderAI.ViewModels;

namespace PromptBuilderAI
{
    public partial class AppSplashScreen : Window
    {
        private readonly DispatcherTimer _timer;
        private int _progress = 0;

        public AppSplashScreen()
        {
            InitializeComponent();

            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromMilliseconds(50);
            _timer.Tick += Timer_Tick;

            Loaded += SplashScreen_Loaded;
        }

        private void SplashScreen_Loaded(object sender, RoutedEventArgs e)
        {
            // Fade in
            var fadeIn = new DoubleAnimation(0, 1, TimeSpan.FromMilliseconds(500));
            BeginAnimation(OpacityProperty, fadeIn);

            // Start progress
            _timer.Start();
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            _progress += 2;
            ProgressBar.Value = _progress;

            if (_progress >= 100)
            {
                _timer.Stop();
                LoadingText.Text = "Ready!";

                // Wait a moment then close
                var closeTimer = new DispatcherTimer();
                closeTimer.Interval = TimeSpan.FromMilliseconds(500);
                closeTimer.Tick += (s, args) =>
                {
                    closeTimer.Stop();
                    ShowMainWindow();
                };
                closeTimer.Start();
            }
        }

        private void ShowMainWindow()
        {
            var fadeOut = new DoubleAnimation(1, 0, TimeSpan.FromMilliseconds(500));
            fadeOut.Completed += (s, e) =>
            {
                var app = (App)Application.Current;
                var mainWindow = new MainWindow
                {
                    DataContext = app._serviceProvider.GetRequiredService<MainViewModel>()
                };

                Application.Current.MainWindow = mainWindow;
                mainWindow.Show();
                this.Close();
            };

            BeginAnimation(OpacityProperty, fadeOut);
        }
    }
}
