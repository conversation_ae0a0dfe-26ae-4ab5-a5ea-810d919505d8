#nullable enable
using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace PromptBuilderAI.Services
{
    /// <summary>
    /// Secure storage service that encrypts sensitive data using Windows DPAPI
    /// </summary>
    public class SecureStorageService : ISecureStorageService
    {
        private readonly string _storageDirectory;

        public SecureStorageService()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            _storageDirectory = Path.Combine(appDataPath, "PromptBuilderAI", "Secure");
            Directory.CreateDirectory(_storageDirectory);
        }

        public async Task StoreSecureValueAsync(string key, string value)
        {
            if (string.IsNullOrWhiteSpace(key))
                throw new ArgumentException("Key cannot be empty", nameof(key));

            if (string.IsNullOrWhiteSpace(value))
                throw new ArgumentException("Value cannot be empty", nameof(value));

            try
            {
                var filePath = GetSecureFilePath(key);
                var encryptedData = ProtectData(value);
                await File.WriteAllBytesAsync(filePath, encryptedData);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to store secure value: {ex.Message}", ex);
            }
        }

        public async Task<string?> GetSecureValueAsync(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
                return null;

            try
            {
                var filePath = GetSecureFilePath(key);
                if (!File.Exists(filePath))
                    return null;

                var encryptedData = await File.ReadAllBytesAsync(filePath);
                return UnprotectData(encryptedData);
            }
            catch (Exception)
            {
                // Return null on any error (file corruption, decryption failure, etc.)
                return null;
            }
        }

        public async Task RemoveSecureValueAsync(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
                return;

            try
            {
                var filePath = GetSecureFilePath(key);
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
            catch (Exception)
            {
                // Ignore errors when removing
            }

            await Task.CompletedTask;
        }

        public Task<bool> HasSecureValueAsync(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
                return Task.FromResult(false);

            try
            {
                var filePath = GetSecureFilePath(key);
                return Task.FromResult(File.Exists(filePath));
            }
            catch (Exception)
            {
                return Task.FromResult(false);
            }
        }

        private string GetSecureFilePath(string key)
        {
            // Create a safe filename from the key
            var safeKey = Convert.ToBase64String(Encoding.UTF8.GetBytes(key))
                .Replace("/", "_")
                .Replace("+", "-")
                .Replace("=", "");

            return Path.Combine(_storageDirectory, $"{safeKey}.dat");
        }

        private static byte[] ProtectData(string data)
        {
            var dataBytes = Encoding.UTF8.GetBytes(data);

            // Use Windows DPAPI for encryption (CurrentUser scope)
            return ProtectedData.Protect(dataBytes, null, DataProtectionScope.CurrentUser);
        }

        private static string UnprotectData(byte[] encryptedData)
        {
            // Use Windows DPAPI for decryption
            var decryptedBytes = ProtectedData.Unprotect(encryptedData, null, DataProtectionScope.CurrentUser);
            return Encoding.UTF8.GetString(decryptedBytes);
        }
    }
}
