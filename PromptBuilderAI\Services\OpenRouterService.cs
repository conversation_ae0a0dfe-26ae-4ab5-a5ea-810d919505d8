using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using PromptBuilderAI.Models;

namespace PromptBuilderAI.Services
{
	public class OpenRouterService : IOpenRouterService
	{
		private readonly HttpClient _httpClient;
		private const string BaseUrl = "https://openrouter.ai/api/v1";
		private const string SiteUrl = "https://github.com/your-repo/prompt-builder-ai";
		private const string SiteName = "Prompt Builder AI";

		public OpenRouterService(HttpClient httpClient)
		{
			_httpClient = httpClient;
		}
		public async Task<string> EnhancePromptAsync(string originalPrompt, string apiKey, string model)
		{
			if (string.IsNullOrWhiteSpace(originalPrompt))
				throw new ArgumentException("Original prompt cannot be empty", nameof(originalPrompt));

			if (string.IsNullOrWhiteSpace(apiKey))
				throw new ArgumentException("API key cannot be empty", nameof(apiKey));

			try
			{
				// Advanced prompt enhancement system
				var enhancementPrompt = BuildAdvancedEnhancementPrompt(originalPrompt);

				var requestBody = new
				{
					model = model ?? "openai/gpt-4",
					messages = new[]
					{
						new {
							role = "system",
							content = GetAdvancedSystemPrompt()
						},
						new {
							role = "user",
							content = enhancementPrompt
						}
					},
					max_tokens = 3000,
					temperature = 0.3
				};

				var json = JsonSerializer.Serialize(requestBody);
				var content = new StringContent(json, Encoding.UTF8, "application/json");

				// Set required headers according to OpenRouter documentation
				_httpClient.DefaultRequestHeaders.Clear();
				_httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");
				_httpClient.DefaultRequestHeaders.Add("HTTP-Referer", SiteUrl);
				_httpClient.DefaultRequestHeaders.Add("X-Title", SiteName);

				var response = await _httpClient.PostAsync($"{BaseUrl}/chat/completions", content);

				if (!response.IsSuccessStatusCode)
				{
					var errorContent = await response.Content.ReadAsStringAsync();
					throw new HttpRequestException($"OpenRouter API error ({response.StatusCode}): {errorContent}");
				}

				var responseContent = await response.Content.ReadAsStringAsync();
				var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);

				if (responseData.TryGetProperty("choices", out var choices) &&
					choices.GetArrayLength() > 0 &&
					choices[0].TryGetProperty("message", out var message) &&
					message.TryGetProperty("content", out var messageContent))
				{
					return messageContent.GetString() ?? throw new InvalidOperationException("Empty response from OpenRouter API");
				}

				throw new InvalidOperationException("Invalid response format from OpenRouter API");
			}
			catch (HttpRequestException)
			{
				throw; // Re-throw HTTP exceptions as-is
			}
			catch (Exception ex)
			{
				throw new Exception($"Failed to enhance prompt: {ex.Message}", ex);
			}
		}
		public async Task<List<OpenRouterModel>> GetAvailableModelsAsync(string apiKey)
		{
			if (string.IsNullOrWhiteSpace(apiKey))
				return GetDefaultModelsAsObjects();

			try
			{
				// Set headers according to OpenRouter documentation
				_httpClient.DefaultRequestHeaders.Clear();
				_httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");
				_httpClient.DefaultRequestHeaders.Add("HTTP-Referer", SiteUrl);
				_httpClient.DefaultRequestHeaders.Add("X-Title", SiteName);

				var response = await _httpClient.GetAsync($"{BaseUrl}/models");

				if (!response.IsSuccessStatusCode)
				{
					// Log the error but return default models
					return GetDefaultModelsAsObjects();
				}

				var content = await response.Content.ReadAsStringAsync();
				var modelsResponse = JsonSerializer.Deserialize<ModelsResponse>(content, new JsonSerializerOptions
				{
					PropertyNameCaseInsensitive = true
				});

				return modelsResponse?.Data ?? GetDefaultModelsAsObjects();
			}
			catch (Exception)
			{
				// Return default models on any error
				return GetDefaultModelsAsObjects();
			}
		}
		public async Task<List<OpenRouterModel>> SearchModelsAsync(string apiKey, string query)
		{
			var allModels = await GetAvailableModelsAsync(apiKey);

			if (string.IsNullOrWhiteSpace(query))
				return allModels;

			return allModels.Where(m =>
				(m.Name?.Contains(query, StringComparison.OrdinalIgnoreCase) == true) ||
				(m.Id?.Contains(query, StringComparison.OrdinalIgnoreCase) == true) ||
				(m.Description?.Contains(query, StringComparison.OrdinalIgnoreCase) == true)
			).ToList();
		}
		public List<string> GetDefaultModels()
		{
			// Comprehensive list of popular models from OpenRouter API (January 2025)
			return new List<string>
			{
				// === LATEST & MOST POPULAR ===
				"deepseek/deepseek-r1-0528",
				"deepseek/deepseek-r1-0528:free",
				"anthropic/claude-opus-4",
				"anthropic/claude-sonnet-4",
				"google/gemini-2.5-pro-preview",
				"google/gemini-2.5-flash-preview-05-20",
				"mistralai/devstral-small",
				"mistralai/mistral-medium-3",

				// === OPENAI MODELS ===
				"openai/gpt-4o",
				"openai/gpt-4-turbo",
				"openai/gpt-4",
				"openai/gpt-3.5-turbo",
				"openai/codex-mini",

				// === ANTHROPIC CLAUDE ===
				"anthropic/claude-3.5-sonnet",
				"anthropic/claude-3-opus",
				"anthropic/claude-3-sonnet",
				"anthropic/claude-3-haiku",
				"anthropic/claude-2.1",

				// === GOOGLE GEMINI ===
				"google/gemini-pro-1.5",
				"google/gemini-pro",
				"google/gemma-3n-e4b-it:free",
				"google/gemma-2b-it",

				// === MISTRAL AI ===
				"mistralai/mistral-large",
				"mistralai/mistral-medium",
				"mistralai/mistral-small",
				"mistralai/mixtral-8x7b-instruct",
				"mistralai/mistral-7b-instruct-v0.2",
				"mistralai/mistral-tiny",

				// === META LLAMA ===
				"meta-llama/llama-3.3-8b-instruct:free",
				"meta-llama/llama-3.1-405b-instruct",
				"meta-llama/llama-3.1-70b-instruct",
				"meta-llama/llama-2-70b-chat",

				// === SPECIALIZED MODELS ===
				"cohere/command-r-plus",
				"arcee-ai/maestro-reasoning",
				"arcee-ai/caller-large",
				"arcee-ai/spotlight",
				"sarvamai/sarvam-m",

				// === COMMUNITY & FINE-TUNED ===
				"nousresearch/deephermes-3-mistral-24b-preview:free",
				"nousresearch/nous-hermes-2-mixtral-8x7b-dpo",
				"thedrummer/valkyrie-49b-v1",
				"gryphe/mythomax-l2-13b",
				"neversleep/noromaid-20b",
				"alpindale/goliath-120b",

				// === UTILITY ===
				"openrouter/auto"
			};
		}
		private List<OpenRouterModel> GetDefaultModelsAsObjects()
		{
			return GetDefaultModels().Select(id => new OpenRouterModel
			{
				Id = id,
				Name = GetFriendlyModelName(id),
				Description = $"Default model: {GetFriendlyModelName(id)}",
				ContextLength = GetModelContextLength(id)
			}).ToList();
		}

		private static string GetFriendlyModelName(string modelId)
		{
			return modelId switch
			{
				// === LATEST & MOST POPULAR ===
				"deepseek/deepseek-r1-0528" => "🔥 DeepSeek R1 0528",
				"deepseek/deepseek-r1-0528:free" => "🆓 DeepSeek R1 0528 (Free)",
				"anthropic/claude-opus-4" => "🚀 Claude Opus 4",
				"anthropic/claude-sonnet-4" => "🚀 Claude Sonnet 4",
				"google/gemini-2.5-pro-preview" => "🚀 Gemini 2.5 Pro Preview",
				"google/gemini-2.5-flash-preview-05-20" => "⚡ Gemini 2.5 Flash Preview",
				"mistralai/devstral-small" => "💻 Devstral Small",
				"mistralai/mistral-medium-3" => "🔥 Mistral Medium 3",

				// === OPENAI MODELS ===
				"openai/gpt-4o" => "GPT-4o",
				"openai/gpt-4-turbo" => "GPT-4 Turbo",
				"openai/gpt-4" => "GPT-4",
				"openai/gpt-3.5-turbo" => "GPT-3.5 Turbo",
				"openai/codex-mini" => "Codex Mini",

				// === ANTHROPIC CLAUDE ===
				"anthropic/claude-3.5-sonnet" => "Claude 3.5 Sonnet",
				"anthropic/claude-3-opus" => "Claude 3 Opus",
				"anthropic/claude-3-sonnet" => "Claude 3 Sonnet",
				"anthropic/claude-3-haiku" => "Claude 3 Haiku",
				"anthropic/claude-2.1" => "Claude 2.1",

				// === GOOGLE GEMINI ===
				"google/gemini-pro-1.5" => "Gemini Pro 1.5",
				"google/gemini-pro" => "Gemini Pro",
				"google/gemma-3n-e4b-it:free" => "🆓 Gemma 3n 4B (Free)",
				"google/gemma-2b-it" => "Gemma 2B",

				// === MISTRAL AI ===
				"mistralai/mistral-large" => "Mistral Large",
				"mistralai/mistral-medium" => "Mistral Medium",
				"mistralai/mistral-small" => "Mistral Small",
				"mistralai/mixtral-8x7b-instruct" => "Mixtral 8x7B",
				"mistralai/mistral-7b-instruct-v0.2" => "Mistral 7B v0.2",
				"mistralai/mistral-tiny" => "Mistral Tiny",

				// === META LLAMA ===
				"meta-llama/llama-3.3-8b-instruct:free" => "🆓 Llama 3.3 8B (Free)",
				"meta-llama/llama-3.1-405b-instruct" => "Llama 3.1 405B",
				"meta-llama/llama-3.1-70b-instruct" => "Llama 3.1 70B",
				"meta-llama/llama-2-70b-chat" => "Llama 2 70B Chat",

				// === SPECIALIZED MODELS ===
				"cohere/command-r-plus" => "Command R+",
				"arcee-ai/maestro-reasoning" => "🧠 Maestro Reasoning",
				"arcee-ai/caller-large" => "📞 Caller Large",
				"arcee-ai/spotlight" => "🔍 Spotlight Vision",
				"sarvamai/sarvam-m" => "🇮🇳 Sarvam-M (Multilingual)",

				// === COMMUNITY & FINE-TUNED ===
				"nousresearch/deephermes-3-mistral-24b-preview:free" => "🆓 DeepHermes 3 24B (Free)",
				"nousresearch/nous-hermes-2-mixtral-8x7b-dpo" => "Nous Hermes 2 Mixtral",
				"thedrummer/valkyrie-49b-v1" => "⚔️ Valkyrie 49B",
				"gryphe/mythomax-l2-13b" => "📚 MythoMax 13B",
				"neversleep/noromaid-20b" => "🌙 Noromaid 20B",
				"alpindale/goliath-120b" => "🏔️ Goliath 120B",

				// === UTILITY ===
				"openrouter/auto" => "🤖 Auto Router (Smart)",

				_ => modelId
			};
		}

		private static int GetModelContextLength(string modelId)
		{
			return modelId switch
			{
				// === LATEST & MOST POPULAR ===
				"deepseek/deepseek-r1-0528" => 128000,
				"deepseek/deepseek-r1-0528:free" => 163840,
				"anthropic/claude-opus-4" => 200000,
				"anthropic/claude-sonnet-4" => 200000,
				"google/gemini-2.5-pro-preview" => 1048576,
				"google/gemini-2.5-flash-preview-05-20" => 1048576,
				"mistralai/devstral-small" => 128000,
				"mistralai/mistral-medium-3" => 131072,

				// === OPENAI MODELS ===
				"openai/gpt-4o" => 128000,
				"openai/gpt-4-turbo" => 128000,
				"openai/gpt-4" => 8192,
				"openai/gpt-3.5-turbo" => 16385,
				"openai/codex-mini" => 200000,

				// === ANTHROPIC CLAUDE ===
				"anthropic/claude-3.5-sonnet" => 200000,
				"anthropic/claude-3-opus" => 200000,
				"anthropic/claude-3-sonnet" => 200000,
				"anthropic/claude-3-haiku" => 200000,
				"anthropic/claude-2.1" => 200000,

				// === GOOGLE GEMINI ===
				"google/gemini-pro-1.5" => 2000000,
				"google/gemini-pro" => 32768,
				"google/gemma-3n-e4b-it:free" => 8192,
				"google/gemma-2b-it" => 8192,

				// === MISTRAL AI ===
				"mistralai/mistral-large" => 128000,
				"mistralai/mistral-medium" => 32768,
				"mistralai/mistral-small" => 32768,
				"mistralai/mixtral-8x7b-instruct" => 32768,
				"mistralai/mistral-7b-instruct-v0.2" => 32768,
				"mistralai/mistral-tiny" => 32768,

				// === META LLAMA ===
				"meta-llama/llama-3.3-8b-instruct:free" => 128000,
				"meta-llama/llama-3.1-405b-instruct" => 131072,
				"meta-llama/llama-3.1-70b-instruct" => 131072,
				"meta-llama/llama-2-70b-chat" => 4096,

				// === SPECIALIZED MODELS ===
				"cohere/command-r-plus" => 128000,
				"arcee-ai/maestro-reasoning" => 128000,
				"arcee-ai/caller-large" => 32768,
				"arcee-ai/spotlight" => 131072,
				"sarvamai/sarvam-m" => 32768,

				// === COMMUNITY & FINE-TUNED ===
				"nousresearch/deephermes-3-mistral-24b-preview:free" => 32768,
				"nousresearch/nous-hermes-2-mixtral-8x7b-dpo" => 32768,
				"thedrummer/valkyrie-49b-v1" => 131072,
				"gryphe/mythomax-l2-13b" => 4096,
				"neversleep/noromaid-20b" => 8192,
				"alpindale/goliath-120b" => 6144,

				// === UTILITY ===
				"openrouter/auto" => 2000000, // Dynamic based on routed model

				_ => 4096
			};
		}

		#region Enhanced Validation Methods

		/// <summary>
		/// Validates the API key by checking access to OpenRouter API
		/// </summary>
		public async Task<ApiValidationResult> ValidateApiKeyAsync(string apiKey)
		{
			var result = new ApiValidationResult();

			if (string.IsNullOrWhiteSpace(apiKey))
			{
				result.IsValid = false;
				result.Message = "API key is required";
				result.ErrorCode = "MISSING_API_KEY";
				return result;
			}

			if (apiKey.Length < 20)
			{
				result.IsValid = false;
				result.Message = "API key appears to be too short";
				result.ErrorCode = "INVALID_API_KEY_FORMAT";
				return result;
			}

			try
			{
				// Test API key by fetching models
				_httpClient.DefaultRequestHeaders.Clear();
				_httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");
				_httpClient.DefaultRequestHeaders.Add("HTTP-Referer", SiteUrl);
				_httpClient.DefaultRequestHeaders.Add("X-Title", SiteName);

				var response = await _httpClient.GetAsync($"{BaseUrl}/models");

				if (response.IsSuccessStatusCode)
				{
					var content = await response.Content.ReadAsStringAsync();
					var modelsResponse = JsonSerializer.Deserialize<ModelsResponse>(content, new JsonSerializerOptions
					{
						PropertyNameCaseInsensitive = true
					});

					result.IsValid = true;
					result.AvailableModelsCount = modelsResponse?.Data?.Count ?? 0;
					result.Message = $"✅ API key is valid! Access to {result.AvailableModelsCount} models.";

					// Try to get account info if available
					try
					{
						var accountResponse = await _httpClient.GetAsync($"{BaseUrl}/auth/key");
						if (accountResponse.IsSuccessStatusCode)
						{
							var accountContent = await accountResponse.Content.ReadAsStringAsync();
							var accountData = JsonSerializer.Deserialize<JsonElement>(accountContent);

							if (accountData.TryGetProperty("data", out var data))
							{
								if (data.TryGetProperty("usage", out var usage) && usage.TryGetProperty("balance", out var balance))
								{
									result.AccountBalance = balance.GetDecimal();
								}
								if (data.TryGetProperty("label", out var label))
								{
									result.AccountType = label.GetString() ?? "Unknown";
								}
							}
						}
					}
					catch
					{
						// Account info is optional, don't fail validation
					}
				}
				else
				{
					result.IsValid = false;
					var errorContent = await response.Content.ReadAsStringAsync();

					result.ErrorCode = response.StatusCode.ToString();
					result.Message = response.StatusCode switch
					{
						System.Net.HttpStatusCode.Unauthorized => "❌ Invalid API key. Please check your OpenRouter API key.",
						System.Net.HttpStatusCode.Forbidden => "❌ API key doesn't have required permissions.",
						System.Net.HttpStatusCode.TooManyRequests => "⚠️ Rate limit exceeded. Please try again later.",
						_ => $"❌ API error ({response.StatusCode}): {errorContent}"
					};
				}
			}
			catch (HttpRequestException ex)
			{
				result.IsValid = false;
				result.Message = $"❌ Network error: {ex.Message}";
				result.ErrorCode = "NETWORK_ERROR";
			}
			catch (Exception ex)
			{
				result.IsValid = false;
				result.Message = $"❌ Unexpected error: {ex.Message}";
				result.ErrorCode = "UNKNOWN_ERROR";
			}

			return result;
		}

		/// <summary>
		/// Validates if a specific model is available and accessible
		/// </summary>
		public async Task<ModelValidationResult> ValidateModelAsync(string apiKey, string modelId)
		{
			var result = new ModelValidationResult();

			if (string.IsNullOrWhiteSpace(modelId))
			{
				result.IsValid = false;
				result.Message = "Model ID is required";
				return result;
			}

			try
			{
				// Get available models
				var models = await GetAvailableModelsAsync(apiKey);
				var targetModel = models.FirstOrDefault(m => m.Id.Equals(modelId, StringComparison.OrdinalIgnoreCase));

				if (targetModel != null)
				{
					result.IsValid = true;
					result.IsAvailable = true;
					result.ModelName = targetModel.Name;
					result.ContextLength = targetModel.ContextLength;
					result.Message = $"✅ Model '{targetModel.Name}' is available";

					if (targetModel.Pricing != null)
					{
						result.PricingInfo = $"Prompt: {targetModel.Pricing.Prompt}, Completion: {targetModel.Pricing.Completion}";
					}
				}
				else
				{
					// Check if it's in our default list
					var defaultModels = GetDefaultModels();
					if (defaultModels.Contains(modelId, StringComparer.OrdinalIgnoreCase))
					{
						result.IsValid = true;
						result.IsAvailable = false; // May be available but not in current API response
						result.ModelName = GetFriendlyModelName(modelId);
						result.ContextLength = GetModelContextLength(modelId);
						result.Message = $"⚠️ Model '{result.ModelName}' is known but may require special access";
					}
					else
					{
						result.IsValid = false;
						result.IsAvailable = false;
						result.Message = $"❌ Model '{modelId}' not found or not accessible";
					}
				}
			}
			catch (Exception ex)
			{
				result.IsValid = false;
				result.Message = $"❌ Error validating model: {ex.Message}";
			}

			return result;
		}

		/// <summary>
		/// Performs a comprehensive test of API key and model by making an actual API call
		/// </summary>
		public async Task<ConnectionTestResult> TestFullConnectionAsync(string apiKey, string modelId)
		{
			var result = new ConnectionTestResult();
			var stopwatch = System.Diagnostics.Stopwatch.StartNew();

			try
			{
				// Step 1: Validate API Key
				result.ApiResult = await ValidateApiKeyAsync(apiKey);
				if (!result.ApiResult.IsValid)
				{
					result.IsSuccessful = false;
					result.Message = result.ApiResult.Message;
					return result;
				}

				// Step 2: Validate Model
				result.ModelResult = await ValidateModelAsync(apiKey, modelId);
				if (!result.ModelResult.IsValid)
				{
					result.IsSuccessful = false;
					result.Message = result.ModelResult.Message;
					return result;
				}

				// Step 3: Test actual API call
				var testPrompt = "Hello! This is a connection test. Please respond with 'Connection successful!'";

				try
				{
					var testResponse = await EnhancePromptAsync(testPrompt, apiKey, modelId);

					stopwatch.Stop();
					result.ResponseTimeMs = stopwatch.ElapsedMilliseconds;
					result.TestResponse = testResponse;

					result.IsSuccessful = true;
					result.Message = $"🎉 Full connection test successful!\n" +
									 $"✅ API Key: Valid ({result.ApiResult.AvailableModelsCount} models)\n" +
									 $"✅ Model: {result.ModelResult.ModelName}\n" +
									 $"✅ Response Time: {result.ResponseTimeMs}ms\n" +
									 $"✅ Test Response: {(testResponse.Length > 50 ? testResponse.Substring(0, 50) + "..." : testResponse)}";
				}
				catch (Exception apiEx)
				{
					stopwatch.Stop();
					result.ResponseTimeMs = stopwatch.ElapsedMilliseconds;
					result.IsSuccessful = false;
					result.Message = $"❌ API call failed: {apiEx.Message}";
				}
			}
			catch (Exception ex)
			{
				stopwatch.Stop();
				result.ResponseTimeMs = stopwatch.ElapsedMilliseconds;
				result.IsSuccessful = false;
				result.Message = $"❌ Connection test failed: {ex.Message}";
			}

			return result;
		}

		#endregion

		#region Advanced Prompt Enhancement

		/// <summary>
		/// Gets the advanced system prompt for AI enhancement
		/// </summary>
		private string GetAdvancedSystemPrompt()
		{
			return @"
# 🤖 Expert Prompt Engineering Assistant

## Your Role
You are a **Master Prompt Engineer** with deep expertise in:
- Software development and architecture
- AI prompt optimization and engineering
- Technical writing and documentation
- Best practices in code generation
- Modern development methodologies

## Core Competencies
1. **Prompt Analysis**: Identify strengths, weaknesses, and gaps in existing prompts
2. **Technical Enhancement**: Add specific technical requirements and constraints
3. **Structure Optimization**: Organize information for maximum AI comprehension
4. **Context Enrichment**: Provide comprehensive background and requirements
5. **Quality Assurance**: Ensure prompts follow best practices and standards

## Enhancement Principles
1. **Clarity**: Make instructions crystal clear and unambiguous
2. **Specificity**: Add concrete technical details and requirements
3. **Completeness**: Cover all aspects of the development task
4. **Structure**: Organize information logically with clear sections
5. **Actionability**: Ensure every instruction is actionable and measurable
6. **Best Practices**: Incorporate industry standards and proven methodologies

## Output Requirements
- Maintain the original intent and core requirements
- Enhance technical depth and specificity
- Add missing context and constraints
- Improve structure and organization
- Include quality assurance measures
- Ensure production-ready guidance
";
		}

		/// <summary>
		/// Builds an advanced enhancement prompt with detailed analysis
		/// </summary>
		private string BuildAdvancedEnhancementPrompt(string originalPrompt)
		{
			var analysisResult = AnalyzePromptStructure(originalPrompt);

			return $@"
# 📝 Prompt Enhancement Request

## Original Prompt Analysis
{analysisResult}

## Original Prompt
```
{originalPrompt}
```

## Enhancement Requirements

Please enhance this prompt using the following systematic approach:

### 1. 🔍 Structure Analysis
- Analyze the current prompt structure and organization
- Identify missing sections or incomplete information
- Evaluate clarity and specificity of instructions

### 2. 🛠️ Technical Enhancement
- Add specific technical requirements and constraints
- Include architecture and design pattern guidance
- Specify security, performance, and scalability requirements
- Add error handling and edge case considerations

### 3. 📚 Context Enrichment
- Provide comprehensive background information
- Add industry best practices and standards
- Include relevant examples and use cases
- Specify testing and validation requirements

### 4. 🎯 Quality Assurance
- Add code quality standards and metrics
- Include documentation requirements
- Specify review and validation criteria
- Add deployment and maintenance considerations

### 5. 📝 Output Formatting
- Organize the enhanced prompt with clear sections
- Use proper markdown formatting and structure
- Include emojis and visual elements for clarity
- Ensure logical flow and readability

## Expected Output
Provide a comprehensive, production-ready prompt that:
- Maintains the original intent and requirements
- Adds significant technical depth and specificity
- Follows prompt engineering best practices
- Is structured for optimal AI comprehension
- Includes all necessary context and constraints
";
		}

		/// <summary>
		/// Analyzes the structure and quality of a prompt
		/// </summary>
		private string AnalyzePromptStructure(string prompt)
		{
			var analysis = new StringBuilder();

			// Basic metrics
			var wordCount = prompt.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length;
			var lineCount = prompt.Split('\n', StringSplitOptions.RemoveEmptyEntries).Length;
			var sectionCount = prompt.Split('#', StringSplitOptions.RemoveEmptyEntries).Length - 1;

			analysis.AppendLine("**Current Prompt Metrics:**");
			analysis.AppendLine($"- Word Count: {wordCount}");
			analysis.AppendLine($"- Line Count: {lineCount}");
			analysis.AppendLine($"- Section Count: {sectionCount}");
			analysis.AppendLine();

			// Structure analysis
			analysis.AppendLine("**Structure Analysis:**");

			if (prompt.Contains("#"))
				analysis.AppendLine("✅ Contains section headers");
			else
				analysis.AppendLine("❌ Missing section headers");

			if (prompt.Contains("**") || prompt.Contains("*"))
				analysis.AppendLine("✅ Uses emphasis formatting");
			else
				analysis.AppendLine("❌ Missing emphasis formatting");

			if (prompt.Contains("1.") || prompt.Contains("-"))
				analysis.AppendLine("✅ Contains lists or numbered items");
			else
				analysis.AppendLine("❌ Missing structured lists");

			// Content analysis
			analysis.AppendLine();
			analysis.AppendLine("**Content Analysis:**");

			var technicalTerms = new[] { "architecture", "pattern", "security", "performance", "scalability", "testing", "documentation" };
			var foundTerms = technicalTerms.Count(term => prompt.ToLower().Contains(term));
			analysis.AppendLine($"- Technical depth: {foundTerms}/{technicalTerms.Length} key terms found");

			if (prompt.ToLower().Contains("requirement"))
				analysis.AppendLine("✅ Contains requirements");
			else
				analysis.AppendLine("❌ Missing explicit requirements");

			if (prompt.ToLower().Contains("example") || prompt.ToLower().Contains("sample"))
				analysis.AppendLine("✅ Includes examples");
			else
				analysis.AppendLine("❌ Missing examples or samples");

			return analysis.ToString();
		}

		#endregion
	}
}
