<Window x:Class="PromptBuilderAI.AppSplashScreen"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Prompt Builder AI"
        Height="400" Width="600"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Topmost="True">

    <Border CornerRadius="15"
            BorderBrush="#673AB7"
            BorderThickness="2">
        <Border.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#181818" Offset="0"/>
                <GradientStop Color="#2D1B69" Offset="0.5"/>
                <GradientStop Color="#181818" Offset="1"/>
            </LinearGradientBrush>
        </Border.Background>

        <Border.Effect>
            <DropShadowEffect Color="#673AB7" BlurRadius="20" ShadowDepth="0" Opacity="0.6"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Logo Section -->
            <StackPanel Grid.Row="1" HorizontalAlignment="Center" Margin="0,40,0,0">
                <materialDesign:PackIcon Kind="RobotExcited"
                                       Width="80" Height="80"
                                       Foreground="#673AB7"
                                       HorizontalAlignment="Center"
                                       Margin="0,0,0,20"/>

                <TextBlock Text="Prompt Builder AI"
                          FontSize="32"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"
                          FontFamily="Segoe UI Light"/>

                <TextBlock Text="Convert your ideas into professional AI prompts"
                          FontSize="14"
                          Foreground="#B0B0B0"
                          HorizontalAlignment="Center"
                          Margin="0,10,0,0"
                          FontStyle="Italic"/>
            </StackPanel>

            <!-- Progress Section -->
            <StackPanel Grid.Row="2" Margin="60,40,60,20">
                <ProgressBar x:Name="ProgressBar"
                           Height="6"
                           Background="#333333"
                           Foreground="#673AB7"
                           BorderThickness="0"
                           Value="0"
                           Maximum="100"/>

                <TextBlock x:Name="LoadingText"
                          Text="Loading..."
                          FontSize="12"
                          Foreground="#B0B0B0"
                          HorizontalAlignment="Center"
                          Margin="0,10,0,0"/>
            </StackPanel>

            <!-- Footer -->
            <StackPanel Grid.Row="3" HorizontalAlignment="Center" Margin="0,20,0,40">
                <TextBlock Text="Version 1.0.0"
                          FontSize="10"
                          Foreground="#673AB7"
                          HorizontalAlignment="Center"/>

                <TextBlock Text="© 2025 Prompt Builder AI"
                          FontSize="10"
                          Foreground="#606060"
                          HorizontalAlignment="Center"
                          Margin="0,5,0,0"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>
