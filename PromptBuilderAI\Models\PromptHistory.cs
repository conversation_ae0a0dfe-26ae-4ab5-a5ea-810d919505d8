using System;
namespace PromptBuilderAI.Models
{
	public class PromptHistory
	{
		public int Id { get; set; }
		public string ProjectIdea { get; set; } = string.Empty;
		public ProjectType ProjectType { get; set; }
		public string GeneratedPrompt { get; set; } = string.Empty;
		public string EnhancedPrompt
		{
			get;
			set;
		}
		public DateTime CreatedAt { get; set; }
		public string SelectedLanguages { get; set; } = string.Empty;
		public string SelectedFrameworks { get; set; } = string.Empty;
		public string SelectedProjectLanguages { get; set; } = string.Empty;
		public ThemeSupport? ThemeSupport { get; set; }
		public ColorPalette? ColorPalette { get; set; }
	}
}
