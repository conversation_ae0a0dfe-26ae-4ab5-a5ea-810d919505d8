using System;
using System.Collections.Generic;
using System.Windows.Media;

namespace PromptBuilderAI.Models
{
	public enum ColorPalette
	{
		Material,
		FlatUI,
		Bootstrap,
		Tailwind,
		AntDesign,
		ChakraUI,
		Bulma,
		Foundation,
		Semantic,
		Custom,
		None
	}

	/// <summary>
	/// Represents a complete color scheme with all necessary colors
	/// </summary>
	public class ColorScheme
	{
		public string Name { get; set; } = string.Empty;
		public ColorPalette PaletteType { get; set; }
		public string Description { get; set; } = string.Empty;
		public string PreviewImage { get; set; } = string.Empty;

		// Primary Colors
		public Color Primary { get; set; }
		public Color PrimaryLight { get; set; }
		public Color PrimaryDark { get; set; }

		// Secondary Colors
		public Color Secondary { get; set; }
		public Color SecondaryLight { get; set; }
		public Color SecondaryDark { get; set; }

		// Accent Colors
		public Color Accent { get; set; }
		public Color Success { get; set; }
		public Color Warning { get; set; }
		public Color Error { get; set; }
		public Color Info { get; set; }

		// Background Colors
		public Color Background { get; set; }
		public Color Surface { get; set; }
		public Color Card { get; set; }

		// Text Colors
		public Color OnPrimary { get; set; }
		public Color OnSecondary { get; set; }
		public Color OnBackground { get; set; }
		public Color OnSurface { get; set; }

		// CSS/Hex Values for web projects
		public Dictionary<string, string> CssVariables { get; set; } = new Dictionary<string, string>();

		// Framework-specific color tokens
		public Dictionary<string, object> FrameworkTokens { get; set; } = new Dictionary<string, object>();
	}

	/// <summary>
	/// Color palette category for better organization
	/// </summary>
	public enum ColorCategory
	{
		Modern,
		Classic,
		Vibrant,
		Minimal,
		Dark,
		Light,
		Gradient,
		Monochrome
	}

	/// <summary>
	/// Accessibility level for color combinations
	/// </summary>
	public enum AccessibilityLevel
	{
		AA,
		AAA,
		Failed
	}

	/// <summary>
	/// Color accessibility information
	/// </summary>
	public class ColorAccessibility
	{
		public double ContrastRatio { get; set; }
		public AccessibilityLevel Level { get; set; }
		public bool IsReadable { get; set; }
		public string Recommendation { get; set; } = string.Empty;
	}
}
