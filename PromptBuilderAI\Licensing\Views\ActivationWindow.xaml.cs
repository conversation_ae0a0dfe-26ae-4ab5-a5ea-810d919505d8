using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using PromptBuilderAI.Licensing.Services;

namespace PromptBuilderAI.Licensing.Views
{
    public partial class ActivationWindow : Window
    {
        private readonly LicenseManager _licenseManager;
        public bool ActivationSuccessful { get; private set; }

        public ActivationWindow()
        {
            InitializeComponent();
            _licenseManager = new LicenseManager();
            
            // Check current license status
            CheckCurrentLicense();
        }

        private void CheckCurrentLicense()
        {
            var currentLicense = _licenseManager.GetCurrentLicense();
            if (currentLicense != null && _licenseManager.IsLicenseValid())
            {
                ShowStatus($"Already activated: {currentLicense.Type}", 
                          $"Expires: {(currentLicense.IsLifetime ? "Never" : currentLicense.ExpiryDate?.ToString("yyyy-MM-dd"))}", 
                          true);
                ActivateButton.Content = "Reactivate";
            }
        }

        private void SerialPart_TextChanged(object sender, TextChangedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox == null) return;

            // Auto-move to next field when current is full
            if (textBox.Text.Length == textBox.MaxLength)
            {
                textBox.MoveFocus(new TraversalRequest(FocusNavigationDirection.Next));
            }

            // Enable/disable activate button
            UpdateActivateButtonState();
        }        private void UpdateActivateButtonState()
        {
            var isComplete = !string.IsNullOrEmpty(SerialPart1.Text) &&
                           !string.IsNullOrEmpty(SerialPart2.Text) &&
                           !string.IsNullOrEmpty(SerialPart3.Text) &&
                           !string.IsNullOrEmpty(SerialPart4.Text) &&
                           SerialPart1.Text.Length == 4 &&
                           SerialPart2.Text.Length == 4 &&
                           SerialPart3.Text.Length == 4 &&
                           SerialPart4.Text.Length == 4;

            ActivateButton.IsEnabled = isComplete;
        }

        private void ActivateButton_Click(object sender, RoutedEventArgs e)
        {
            var serialNumber = $"{SerialPart1.Text}-{SerialPart2.Text}-{SerialPart3.Text}-{SerialPart4.Text}";
            
            try
            {
                ActivateButton.IsEnabled = false;
                ActivateButton.Content = "Activating...";

                var result = _licenseManager.ActivateLicense(serialNumber);

                if (result.Success)
                {
                    ShowStatus("Activation Successful!", 
                              $"License Type: {result.LicenseInfo?.Type}", 
                              true);
                    
                    ActivationSuccessful = true;
                    
                    MessageBox.Show("License activated successfully!\nAll features are now unlocked.", 
                                  "Activation Successful", 
                                  MessageBoxButton.OK, 
                                  MessageBoxImage.Information);
                    
                    DialogResult = true;
                    Close();
                }
                else
                {
                    ShowStatus("Activation Failed", result.ErrorMessage, false);
                }
            }
            catch (Exception ex)
            {
                ShowStatus("Activation Error", ex.Message, false);
            }
            finally
            {
                ActivateButton.IsEnabled = true;
                ActivateButton.Content = "Activate License";
            }
        }        private void StartTrialButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _licenseManager.StartTrial();
                
                ShowStatus("Trial Started", "30-day trial activated", true);
                
                MessageBox.Show("30-day trial started!\nSome features are limited in trial mode.", 
                              "Trial Started", 
                              MessageBoxButton.OK, 
                              MessageBoxImage.Information);
                
                ActivationSuccessful = true;
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to start trial: {ex.Message}", 
                              "Error", 
                              MessageBoxButton.OK, 
                              MessageBoxImage.Error);
            }
        }

        private void BuyLicenseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Open purchase page
                Process.Start(new ProcessStartInfo
                {
                    FileName = "https://promptbuilderai.com/purchase",
                    UseShellExecute = true
                });
            }
            catch
            {
                MessageBox.Show("Please visit: https://promptbuilderai.com/purchase", 
                              "Purchase License", 
                              MessageBoxButton.OK, 
                              MessageBoxImage.Information);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }        private void ShowStatus(string title, string details, bool isSuccess)
        {
            StatusCard.Visibility = Visibility.Visible;
            StatusText.Text = title;
            StatusDetails.Text = details;
            
            if (isSuccess)
            {
                StatusText.Foreground = System.Windows.Media.Brushes.Green;
            }
            else
            {
                StatusText.Foreground = System.Windows.Media.Brushes.Red;
            }
        }
    }
}