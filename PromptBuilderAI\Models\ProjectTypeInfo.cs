using System;
using System.Collections.Generic;
namespace PromptBuilderAI.Models
{
	public class ProjectTypeInfo
	{
		public ProjectType Type { get; set; }
		public string DisplayName { get; set; } = string.Empty;
		public List<string> AvailableLanguages { get; set; } = new List<string>();
		public List<string> AvailableFrameworks { get; set; } = new List<string>();
		public bool SupportsThemes { get; set; }
		public bool SupportsColorPalette { get; set; }

		// Advanced filtering: Language-specific frameworks
		public Dictionary<string, List<string>> LanguageFrameworks { get; set; } = new Dictionary<string, List<string>>();
		// Framework-specific libraries/packages
		public Dictionary<string, List<string>> FrameworkLibraries { get; set; } = new Dictionary<string, List<string>>();
	}
}
