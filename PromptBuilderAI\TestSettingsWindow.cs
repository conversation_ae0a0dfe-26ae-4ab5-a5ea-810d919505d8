using System;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using PromptBuilderAI.Services;
using PromptBuilderAI.ViewModels;
using PromptBuilderAI.Views;

namespace PromptBuilderAI
{
    /// <summary>
    /// Test class to verify Settings window functionality
    /// </summary>
    public static class TestSettingsWindow
    {
        public static void TestOpenSettings()
        {
            try
            {
                Console.WriteLine("Testing Settings Window...");
                
                // Create services manually for testing
                var secureStorage = new SecureStorageService();
                var httpClient = new System.Net.Http.HttpClient();
                var openRouterService = new OpenRouterService(httpClient);
                
                // Create SettingsViewModel
                var settingsViewModel = new SettingsViewModel(secureStorage, openRouterService);
                
                // Create and show Settings window
                var settingsWindow = new SettingsWindow(settingsViewModel);
                
                Console.WriteLine("Settings window created successfully!");
                
                // Show the window
                settingsWindow.ShowDialog();
                
                Console.WriteLine("Settings window closed.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error testing settings window: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
