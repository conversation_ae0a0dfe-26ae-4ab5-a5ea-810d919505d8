using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using SerialGeneratorTool.Models;
using SerialGeneratorTool.Services;

namespace SerialGeneratorTool.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly SerialGenerator _serialGenerator = new();

        [ObservableProperty]
        private bool isLifetimeLicense = true;

        [ObservableProperty]
        private string selectedPreset = "Custom";

        [ObservableProperty]
        private int customDays = 30;

        [ObservableProperty]
        private int customMonths = 0;

        [ObservableProperty]
        private int customYears = 0;

        [ObservableProperty]
        private DateTime expiryDate = DateTime.Now.AddDays(30);

        [ObservableProperty]
        private string generatedSerial = "";

        [ObservableProperty]
        private ObservableCollection<SerialStatusRecord> serialDatabase = new();

        [ObservableProperty]
        private string validationSerial = "";

        public List<string> DurationPresets { get; } = new()
        {
            "7 Days Trial",
            "30 Days Trial",
            "3 Months",
            "6 Months", 
            "1 Year",
            "2 Years",
            "Custom"
        };

        public MainViewModel()
        {
            RefreshSerialDatabase();
        }        [RelayCommand]
        private void GenerateSerial()
        {
            LicenseType type;
            int totalDays;

            if (IsLifetimeLicense)
            {
                type = LicenseType.Lifetime;
                totalDays = 0;
            }
            else
            {
                totalDays = CalculateTotalDays();
                type = totalDays <= 30 ? LicenseType.Trial : LicenseType.TimeLimited;
            }

            var serial = _serialGenerator.GenerateSerial(type, totalDays);
            GeneratedSerial = serial;

            // إضافة للقائمة
            var record = new SerialStatusRecord
            {
                Serial = serial,
                Type = type.ToString(),
                Duration = IsLifetimeLicense ? "Lifetime" : $"{totalDays} Days",
                CreatedDate = DateTime.Now,
                IsUsed = false
            };

            SerialDatabase.Insert(0, record);
        }

        [RelayCommand]
        private void ApplyPreset(string preset)
        {
            switch (preset)
            {
                case "7 Days Trial":
                    CustomDays = 7; CustomMonths = 0; CustomYears = 0;
                    break;
                case "30 Days Trial":
                    CustomDays = 30; CustomMonths = 0; CustomYears = 0;
                    break;
                case "3 Months":
                    CustomDays = 0; CustomMonths = 3; CustomYears = 0;
                    break;
                case "6 Months":
                    CustomDays = 0; CustomMonths = 6; CustomYears = 0;
                    break;
                case "1 Year":
                    CustomDays = 0; CustomMonths = 0; CustomYears = 1;
                    break;
                case "2 Years":
                    CustomDays = 0; CustomMonths = 0; CustomYears = 2;
                    break;
            }

            UpdateExpiryDate();
        }        [RelayCommand]
        private void CopyToClipboard()
        {
            if (!string.IsNullOrEmpty(GeneratedSerial))
            {
                Clipboard.SetText(GeneratedSerial);
                MessageBox.Show("Serial copied to clipboard!", "Success", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        [RelayCommand]
        private void ValidateSerial()
        {
            if (string.IsNullOrEmpty(ValidationSerial)) return;

            var info = _serialGenerator.GetSerialInfo(ValidationSerial);
            if (info == null)
            {
                MessageBox.Show("Serial number not found!", "Validation Result",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var status = info.IsUsed ? "USED" : "AVAILABLE";
            var message = $"Serial: {ValidationSerial}\n" +
                         $"Status: {status}\n" +
                         $"Type: {info.Type}\n" +
                         $"Created: {info.CreatedDate:yyyy-MM-dd}\n";

            if (info.IsUsed)
            {
                message += $"Activated: {info.ActivationDate:yyyy-MM-dd HH:mm}\n" +
                          $"Machine: {info.MachineFingerprint}";
            }

            MessageBox.Show(message, "Serial Status",
                           MessageBoxButton.OK, MessageBoxImage.Information);
        }        [RelayCommand]
        private void RefreshSerialDatabase()
        {
            SerialDatabase.Clear();
            
            // تحميل جميع السيريالات من قاعدة البيانات
            var allSerials = LoadAllSerials();
            foreach (var serial in allSerials.OrderByDescending(x => x.CreatedDate))
            {
                SerialDatabase.Add(serial);
            }
        }

        [RelayCommand]
        private void ExportUnusedSerials()
        {
            var unusedSerials = SerialDatabase.Where(x => !x.IsUsed).ToList();

            var csv = "Serial,Type,Duration,Created\n";
            foreach (var serial in unusedSerials)
            {
                csv += $"{serial.Serial},{serial.Type},{serial.Duration},{serial.CreatedDate:yyyy-MM-dd}\n";
            }

            var fileName = $"UnusedSerials_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
            File.WriteAllText(fileName, csv);

            MessageBox.Show($"Exported {unusedSerials.Count} unused serials to {fileName}",
                           "Export Complete", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void ClearHistory()
        {
            var result = MessageBox.Show("Are you sure you want to clear all serial history?",
                                       "Confirm Clear", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                SerialDatabase.Clear();
                // يمكن إضافة حذف من قاعدة البيانات هنا إذا لزم الأمر
            }
        }        private int CalculateTotalDays()
        {
            return CustomDays + (CustomMonths * 30) + (CustomYears * 365);
        }

        private void UpdateExpiryDate()
        {
            var totalDays = CalculateTotalDays();
            ExpiryDate = DateTime.Now.AddDays(totalDays);
        }

        private List<SerialStatusRecord> LoadAllSerials()
        {
            var records = new List<SerialStatusRecord>();
            
            try
            {
                if (File.Exists("ActivationDatabase.json"))
                {
                    var json = File.ReadAllText("ActivationDatabase.json");
                    var database = System.Text.Json.JsonSerializer.Deserialize<ActivationDatabase>(json);
                    
                    if (database?.UsedSerials != null)
                    {
                        foreach (var serial in database.UsedSerials)
                        {
                            records.Add(new SerialStatusRecord
                            {
                                Serial = serial.SerialNumber,
                                Type = serial.Type.ToString(),
                                Duration = serial.Type == LicenseType.Lifetime ? "Lifetime" : $"{serial.DurationDays} Days",
                                CreatedDate = serial.CreatedDate,
                                IsUsed = serial.IsUsed,
                                ActivationDate = serial.ActivationDate
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading database: {ex.Message}", "Error",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }

            return records;
        }
    }
}