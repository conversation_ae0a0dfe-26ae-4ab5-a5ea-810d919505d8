using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Security.Cryptography;
using System.Text;
using PromptBuilderAI.Licensing.Models;

namespace PromptBuilderAI.Licensing.Services
{
    public class LicenseManager
    {
        private const string LICENSE_FILE = "license.dat";
        private const string ACTIVATION_DB = "ActivationDatabase.json";
        private LicenseInfo? _currentLicense;

        public LicenseInfo? GetCurrentLicense()
        {
            if (_currentLicense != null) return _currentLicense;

            try
            {
                if (!File.Exists(LICENSE_FILE)) return null;

                var encryptedData = File.ReadAllText(LICENSE_FILE);
                var decryptedData = DecryptLicenseData(encryptedData);
                var licenseData = JsonSerializer.Deserialize<LicenseInfo>(decryptedData);
                
                if (licenseData != null && IsLicenseValid(licenseData))
                {
                    _currentLicense = licenseData;
                    return _currentLicense;
                }
            }
            catch
            {
                // License file corrupted or invalid
            }

            return null;
        }

        public bool IsLicenseValid()
        {
            var license = GetCurrentLicense();
            if (license == null) return false;

            // Check expiry
            if (license.IsExpired) return false;

            // Check machine fingerprint
            var currentFingerprint = GenerateMachineFingerprint();
            if (license.MachineFingerprint != currentFingerprint) return false;

            return true;
        }        public ActivationResult ActivateLicense(string serialNumber)
        {
            try
            {
                var machineFingerprint = GenerateMachineFingerprint();
                var result = ValidateAndActivateSerial(serialNumber, machineFingerprint);

                if (result.Success && result.LicenseInfo != null)
                {
                    SaveLicenseInfo(result.LicenseInfo);
                    _currentLicense = result.LicenseInfo;
                }

                return result;
            }
            catch (Exception ex)
            {
                return new ActivationResult
                {
                    Success = false,
                    ErrorMessage = $"Activation failed: {ex.Message}"
                };
            }
        }

        public bool IsFeatureEnabled(string featureName)
        {
            var license = GetCurrentLicense();
            if (license == null || !IsLicenseValid()) return false;

            // Trial version limitations
            if (license.Type == LicenseType.Trial)
            {
                return featureName switch
                {
                    "BasicPromptGeneration" => true,
                    "ColorSelection" => true,
                    "ThemeSupport" => true,
                    "AIEnhancement" => false,
                    "AdvancedTemplates" => false,
                    "ExportOptions" => false,
                    "HistoryManagement" => true,
                    _ => false
                };
            }

            // Full license - all features enabled
            return true;
        }        public void StartTrial()
        {
            var trialLicense = new LicenseInfo
            {
                SerialNumber = "TRIAL-" + Guid.NewGuid().ToString()[..8].ToUpper(),
                Type = LicenseType.Trial,
                CreatedDate = DateTime.Now,
                ExpiryDate = DateTime.Now.AddDays(30),
                ActivationDate = DateTime.Now,
                MachineFingerprint = GenerateMachineFingerprint(),
                ActivationId = Guid.NewGuid().ToString(),
                IsValid = true,
                EnabledFeatures = new() { "BasicPromptGeneration", "ColorSelection", "ThemeSupport", "HistoryManagement" }
            };

            SaveLicenseInfo(trialLicense);
            _currentLicense = trialLicense;
        }

        public void RemoveLicense()
        {
            try
            {
                if (File.Exists(LICENSE_FILE))
                {
                    File.Delete(LICENSE_FILE);
                }
                _currentLicense = null;
            }
            catch
            {
                // Ignore errors
            }
        }

        private ActivationResult ValidateAndActivateSerial(string serialNumber, string machineFingerprint)
        {
            // This would normally connect to activation database
            // For now, we'll simulate the validation
            
            if (string.IsNullOrEmpty(serialNumber) || serialNumber.Length < 19)
            {
                return new ActivationResult
                {
                    Success = false,
                    ErrorMessage = "Invalid serial number format."
                };
            }

            // Parse serial number format: PB25-XXXX-XXXX-XXXX
            var parts = serialNumber.Split('-');
            if (parts.Length != 4 || parts[0] != "PB25")
            {
                return new ActivationResult
                {
                    Success = false,
                    ErrorMessage = "Invalid serial number."
                };
            }            // Decode license type from serial
            var typeCode = parts[1];
            var licenseType = typeCode switch
            {
                "LT00" => LicenseType.Lifetime,
                var t when t.StartsWith("T") => LicenseType.TimeLimited,
                _ => LicenseType.Trial
            };

            // Calculate expiry date
            DateTime? expiryDate = null;
            if (licenseType != LicenseType.Lifetime && typeCode.StartsWith("T"))
            {
                if (int.TryParse(typeCode[1..], out var days))
                {
                    expiryDate = DateTime.Now.AddDays(days);
                }
            }

            var licenseInfo = new LicenseInfo
            {
                SerialNumber = serialNumber,
                Type = licenseType,
                CreatedDate = DateTime.Now,
                ExpiryDate = expiryDate,
                ActivationDate = DateTime.Now,
                MachineFingerprint = machineFingerprint,
                ActivationId = Guid.NewGuid().ToString(),
                IsValid = true,
                EnabledFeatures = GetEnabledFeatures(licenseType)
            };

            return new ActivationResult
            {
                Success = true,
                LicenseInfo = licenseInfo,
                ActivationId = licenseInfo.ActivationId
            };
        }

        private List<string> GetEnabledFeatures(LicenseType type)
        {
            return type switch
            {
                LicenseType.Trial => new() { "BasicPromptGeneration", "ColorSelection", "ThemeSupport", "HistoryManagement" },
                LicenseType.TimeLimited or LicenseType.Lifetime => new() { 
                    "BasicPromptGeneration", "ColorSelection", "ThemeSupport", "HistoryManagement",
                    "AIEnhancement", "AdvancedTemplates", "ExportOptions" 
                },
                _ => new()
            };
        }        private bool IsLicenseValid(LicenseInfo license)
        {
            if (license.IsExpired) return false;
            
            var currentFingerprint = GenerateMachineFingerprint();
            return license.MachineFingerprint == currentFingerprint;
        }

        private void SaveLicenseInfo(LicenseInfo license)
        {
            var data = JsonSerializer.Serialize(license, new JsonSerializerOptions { WriteIndented = true });
            var encryptedData = EncryptLicenseData(data);
            File.WriteAllText(LICENSE_FILE, encryptedData);
        }

        private string GenerateMachineFingerprint()
        {
            var machineInfo = $"{Environment.MachineName}-{Environment.UserName}-{Environment.OSVersion}";
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(machineInfo));
            return Convert.ToBase64String(hash)[..16];
        }

        private string EncryptLicenseData(string data)
        {
            // Simple encryption for demo - in production use proper encryption
            var bytes = Encoding.UTF8.GetBytes(data);
            return Convert.ToBase64String(bytes);
        }

        private string DecryptLicenseData(string encryptedData)
        {
            // Simple decryption for demo - in production use proper decryption
            var bytes = Convert.FromBase64String(encryptedData);
            return Encoding.UTF8.GetString(bytes);
        }
    }
}