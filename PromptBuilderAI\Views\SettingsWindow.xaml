<Window x:Class="PromptBuilderAI.Views.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:converters="clr-namespace:PromptBuilderAI.Converters"
        Title="Settings"
        Height="600"
        Width="800"
        MinHeight="400"
        MinWidth="600"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:BooleanToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <materialDesign:BooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" TrueValue="Collapsed" FalseValue="Visible"/>
    </Window.Resources>

    <materialDesign:DialogHost Identifier="SettingsDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <materialDesign:ColorZone Grid.Row="0"
                                      Mode="PrimaryMid"
                                      Padding="16">
                <DockPanel>
                    <materialDesign:PackIcon Kind="Settings"
                                           Width="24"
                                           Height="24"
                                           VerticalAlignment="Center"
                                           Foreground="White"/>
                    <TextBlock Text="Settings"
                             Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                             VerticalAlignment="Center"
                             Margin="16,0,0,0"
                             Foreground="White"/>
                </DockPanel>
            </materialDesign:ColorZone>

            <!-- Content Area -->
            <ScrollViewer Grid.Row="1"
                         VerticalScrollBarVisibility="Auto"
                         Padding="24">
                <StackPanel>
                    <!-- OpenRouter API Configuration -->
                    <materialDesign:Card Padding="24"
                                       Margin="0,0,0,16">
                        <StackPanel>
                            <DockPanel Margin="0,0,0,16">
                                <materialDesign:PackIcon Kind="Api"
                                                       Width="24" Height="24"
                                                       VerticalAlignment="Center"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                <TextBlock Text="OpenRouter API Configuration"
                                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                         VerticalAlignment="Center"
                                         Margin="12,0,0,0"/>
                            </DockPanel>

                            <TextBlock Text="Configure your OpenRouter.ai API key and select your preferred AI model for prompt enhancement."
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Opacity="0.7"
                                     TextWrapping="Wrap"
                                     Margin="0,0,0,24"/>

                            <!-- API Key Section -->
                            <TextBlock Text="API Key"
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     Margin="0,0,0,8"/>

                            <Grid Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBox x:Name="ApiKeyBox"
                                       Grid.Column="0"
                                       Text="{Binding ApiKey, UpdateSourceTrigger=PropertyChanged}"
                                       materialDesign:HintAssist.Hint="Enter your OpenRouter API key"
                                       materialDesign:HintAssist.IsFloating="True"
                                       Style="{StaticResource MaterialDesignTextBox}"
                                       Margin="0,0,8,0">
                                    <TextBox.InputBindings>
                                        <KeyBinding Key="Enter" Command="{Binding ValidateApiKeyCommand}"/>
                                    </TextBox.InputBindings>
                                </TextBox>

                                <Button Grid.Column="1"
                                       Content="Validate"
                                       Style="{StaticResource MaterialDesignOutlinedButton}"
                                       Command="{Binding ValidateApiKeyCommand}"
                                       IsEnabled="{Binding IsValidatingApiKey, Converter={StaticResource InverseBooleanConverter}}"
                                       Margin="0,0,8,0">
                                    <Button.ContentTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="CheckCircle"
                                                                       Width="16" Height="16"
                                                                       VerticalAlignment="Center"
                                                                       Margin="0,0,4,0"/>
                                                <TextBlock Text="Validate" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </Button.ContentTemplate>
                                </Button>

                                <Button Grid.Column="2"
                                       Content="Clear"
                                       Style="{StaticResource MaterialDesignOutlinedButton}"
                                       Command="{Binding ClearApiKeyCommand}"
                                       Foreground="{DynamicResource MaterialDesignValidationErrorBrush}"/>
                            </Grid>

                            <!-- API Key Status -->
                            <Border Background="{DynamicResource MaterialDesignCardBackground}"
                                   CornerRadius="4"
                                   Padding="12"
                                   Margin="0,0,0,16"
                                   Visibility="{Binding ApiKeyStatus, Converter={StaticResource StringToVisibilityConverter}}">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Status Icon -->
                                    <materialDesign:PackIcon Grid.Column="0"
                                                           Width="20" Height="20"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0">
                                        <materialDesign:PackIcon.Style>
                                            <Style TargetType="materialDesign:PackIcon">
                                                <Setter Property="Kind" Value="Information"/>
                                                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding HasValidApiKey}" Value="True">
                                                        <Setter Property="Kind" Value="CheckCircle"/>
                                                        <Setter Property="Foreground" Value="Green"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsValidatingApiKey}" Value="True">
                                                        <Setter Property="Kind" Value="Loading"/>
                                                        <Setter Property="Foreground" Value="Orange"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </materialDesign:PackIcon.Style>
                                    </materialDesign:PackIcon>

                                    <!-- Status Text -->
                                    <TextBlock Grid.Column="1"
                                             Text="{Binding ApiKeyStatus}"
                                             VerticalAlignment="Center"
                                             TextWrapping="Wrap"
                                             Style="{StaticResource MaterialDesignBody2TextBlock}"/>

                                    <!-- Loading Indicator -->
                                    <ProgressBar Grid.Column="2"
                                               Style="{StaticResource MaterialDesignCircularProgressBar}"
                                               Width="20" Height="20"
                                               IsIndeterminate="True"
                                               Visibility="{Binding IsValidatingApiKey, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                </Grid>
                            </Border>

                            <!-- Connection Status -->
                            <Border Background="{DynamicResource MaterialDesignDivider}"
                                   CornerRadius="4"
                                   Padding="12,8"
                                   Margin="0,0,0,16"
>
                                <DockPanel>
                                    <materialDesign:PackIcon Kind="Information"
                                                           Width="16" Height="16"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding StatusMessage}"
                                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                                             VerticalAlignment="Center"
                                             TextWrapping="Wrap"/>
                                </DockPanel>
                            </Border>

                            <!-- Model Selection -->
                            <TextBlock Text="AI Model"
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     Margin="0,0,0,8"/>

                            <!-- Model Search Box -->
                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <TextBox Grid.Column="0"
                                       Text="{Binding ModelSearchText, UpdateSourceTrigger=PropertyChanged}"
                                       materialDesign:HintAssist.Hint="🔍 Search AI models (e.g., 'gpt', 'claude', 'gemini')"
                                       materialDesign:HintAssist.IsFloating="True"
                                       Style="{StaticResource MaterialDesignTextBox}"
                                       Margin="0,0,8,0">
                                    <TextBox.InputBindings>
                                        <KeyBinding Key="Escape" Command="{Binding ClearModelSearchCommand}"/>
                                    </TextBox.InputBindings>
                                </TextBox>

                                <Button Grid.Column="1"
                                       Content="Clear"
                                       Style="{StaticResource MaterialDesignOutlinedButton}"
                                       Command="{Binding ClearModelSearchCommand}"
                                       Visibility="{Binding ModelSearchText, Converter={StaticResource StringToVisibilityConverter}}"
                                       Padding="8,4">
                                    <Button.ContentTemplate>
                                        <DataTemplate>
                                            <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                                        </DataTemplate>
                                    </Button.ContentTemplate>
                                </Button>
                            </Grid>

                            <!-- Model Selection -->
                            <Grid Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <ComboBox Grid.Column="0"
                                        ItemsSource="{Binding FilteredModels}"
                                        SelectedValue="{Binding SelectedModel}"
                                        SelectedValuePath="Id"
                                        materialDesign:HintAssist.Hint="Select AI model from filtered results"
                                        materialDesign:HintAssist.IsFloating="True"
                                        Style="{StaticResource MaterialDesignComboBox}"
                                        Margin="0,0,8,0"
                                        IsEditable="False">
                                    <ComboBox.InputBindings>
                                        <KeyBinding Key="Enter" Command="{Binding ValidateModelCommand}"/>
                                    </ComboBox.InputBindings>
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Vertical" Margin="0,4">
                                                <TextBlock Text="{Binding Name}"
                                                         FontWeight="Medium"
                                                         Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                                                <TextBlock Text="{Binding Id}"
                                                         Opacity="0.7"
                                                         FontSize="11"
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                                                <TextBlock Opacity="0.6"
                                                         FontSize="10"
                                                         Style="{StaticResource MaterialDesignCaptionTextBlock}">
                                                    <Run Text="Context: "/>
                                                    <Run Text="{Binding ContextLength, StringFormat='{}{0:N0} tokens'}"/>
                                                </TextBlock>
                                            </StackPanel>
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                </ComboBox>

                                <Button Grid.Column="1"
                                       Content="Validate"
                                       Style="{StaticResource MaterialDesignOutlinedButton}"
                                       Command="{Binding ValidateModelCommand}"
                                       IsEnabled="{Binding IsValidatingModel, Converter={StaticResource InverseBooleanConverter}}"
                                       Margin="0,0,8,0">
                                    <Button.ContentTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="CheckCircle"
                                                                       Width="16" Height="16"
                                                                       VerticalAlignment="Center"
                                                                       Margin="0,0,4,0"/>
                                                <TextBlock Text="Validate" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </Button.ContentTemplate>
                                </Button>

                                <Button Grid.Column="2"
                                       Content="Refresh"
                                       Style="{StaticResource MaterialDesignOutlinedButton}"
                                       Command="{Binding LoadAvailableModelsCommand}"
>
                                    <Button.ContentTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Refresh"
                                                                       Width="16" Height="16"
                                                                       VerticalAlignment="Center"
                                                                       Margin="0,0,4,0"/>
                                                <TextBlock Text="Refresh" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </Button.ContentTemplate>
                                </Button>
                            </Grid>

                            <!-- Search Results Info -->
                            <Border Background="{DynamicResource MaterialDesignDivider}"
                                   CornerRadius="4"
                                   Padding="12,8"
                                   Margin="0,0,0,8"
                                   Visibility="{Binding ModelSearchText, Converter={StaticResource StringToVisibilityConverter}}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Magnify"
                                                           Width="16" Height="16"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock VerticalAlignment="Center"
                                             Style="{StaticResource MaterialDesignBody2TextBlock}">
                                        <Run Text="Search results for '"/>
                                        <Run Text="{Binding ModelSearchText}" FontWeight="Medium"/>
                                        <Run Text="': "/>
                                        <Run Text="{Binding FilteredModelsCount, Mode=OneWay}" FontWeight="Medium"/>
                                        <Run Text=" models found"/>
                                    </TextBlock>

                                    <!-- Search Loading Indicator -->
                                    <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                               Width="16" Height="16"
                                               IsIndeterminate="True"
                                               Margin="8,0,0,0"
                                               Visibility="{Binding IsSearchingModels, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                </StackPanel>
                            </Border>

                            <!-- Loading Indicator -->
                            <StackPanel Orientation="Horizontal"
                                      Visibility="{Binding IsLoadingModels, Converter={StaticResource BooleanToVisibilityConverter}}"
                                      Margin="0,0,0,16">
                                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                           Width="16" Height="16"
                                           IsIndeterminate="True"
                                           Margin="0,0,8,0"/>
                                <TextBlock Text="Loading models..."
                                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                                         VerticalAlignment="Center"/>
                            </StackPanel>

                            <!-- Model Status -->
                            <Border Background="{DynamicResource MaterialDesignCardBackground}"
                                   CornerRadius="4"
                                   Padding="12"
                                   Margin="0,0,0,16"
                                   Visibility="{Binding ModelStatus, Converter={StaticResource StringToVisibilityConverter}}">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Status Icon -->
                                    <materialDesign:PackIcon Grid.Column="0"
                                                           Width="20" Height="20"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0">
                                        <materialDesign:PackIcon.Style>
                                            <Style TargetType="materialDesign:PackIcon">
                                                <Setter Property="Kind" Value="Information"/>
                                                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding HasValidModel}" Value="True">
                                                        <Setter Property="Kind" Value="CheckCircle"/>
                                                        <Setter Property="Foreground" Value="Green"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsValidatingModel}" Value="True">
                                                        <Setter Property="Kind" Value="Loading"/>
                                                        <Setter Property="Foreground" Value="Orange"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </materialDesign:PackIcon.Style>
                                    </materialDesign:PackIcon>

                                    <!-- Status Text -->
                                    <TextBlock Grid.Column="1"
                                             Text="{Binding ModelStatus}"
                                             VerticalAlignment="Center"
                                             TextWrapping="Wrap"
                                             Style="{StaticResource MaterialDesignBody2TextBlock}"/>

                                    <!-- Loading Indicator -->
                                    <ProgressBar Grid.Column="2"
                                               Style="{StaticResource MaterialDesignCircularProgressBar}"
                                               Width="20" Height="20"
                                               IsIndeterminate="True"
                                               Visibility="{Binding IsValidatingModel, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                </Grid>
                            </Border>

                            <!-- Connection Details -->
                            <materialDesign:Card Background="{DynamicResource MaterialDesignCardBackground}"
                                               Padding="16"
                                               Margin="0,0,0,16"
                                               Visibility="{Binding ConnectionDetails, Converter={StaticResource StringToVisibilityConverter}}">
                                <StackPanel>
                                    <DockPanel Margin="0,0,0,8">
                                        <materialDesign:PackIcon Kind="InformationOutline"
                                                               Width="20" Height="20"
                                                               VerticalAlignment="Center"
                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                        <TextBlock Text="Connection Details"
                                                 Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                                 VerticalAlignment="Center"
                                                 Margin="8,0,0,0"/>
                                    </DockPanel>

                                    <TextBlock Text="{Binding ConnectionDetails}"
                                             TextWrapping="Wrap"
                                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                                             Opacity="0.8"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Full Connection Test -->
                            <Grid Margin="0,0,0,16">
                                <Button Content="🧪 Full Connection Test"
                                       Style="{StaticResource MaterialDesignRaisedButton}"
                                       Command="{Binding TestConnectionCommand}"
                                       IsEnabled="{Binding IsTestingConnection, Converter={StaticResource InverseBooleanConverter}}"
                                       HorizontalAlignment="Center"
                                       Padding="24,12">
                                    <Button.ContentTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="TestTube"
                                                                       Width="20" Height="20"
                                                                       VerticalAlignment="Center"
                                                                       Margin="0,0,8,0"/>
                                                <TextBlock Text="Full Connection Test"
                                                         VerticalAlignment="Center"
                                                         FontWeight="Medium"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </Button.ContentTemplate>
                                </Button>
                            </Grid>

                            <!-- Search Help -->
                            <materialDesign:Card Background="{DynamicResource MaterialDesignCardBackground}"
                                               Padding="16"
                                               Margin="0,0,0,16">
                                <StackPanel>
                                    <DockPanel Margin="0,0,0,8">
                                        <materialDesign:PackIcon Kind="Magnify"
                                                               Width="20" Height="20"
                                                               VerticalAlignment="Center"
                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                        <TextBlock Text="Search Tips"
                                                 Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                                 VerticalAlignment="Center"
                                                 Margin="8,0,0,0"/>
                                    </DockPanel>

                                    <TextBlock TextWrapping="Wrap"
                                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                                             Opacity="0.8">
                                        <Run Text="• Search by provider: "/>
                                        <Run Text="'openai', 'anthropic', 'google'" FontWeight="Medium"/>
                                        <LineBreak/>
                                        <Run Text="• Search by model type: "/>
                                        <Run Text="'gpt', 'claude', 'gemini'" FontWeight="Medium"/>
                                        <LineBreak/>
                                        <Run Text="• Search by features: "/>
                                        <Run Text="'free', 'vision', 'coding'" FontWeight="Medium"/>
                                        <LineBreak/>
                                        <Run Text="• Use "/>
                                        <Run Text="Escape" FontWeight="Medium"/>
                                        <Run Text=" key to clear search"/>
                                    </TextBlock>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Help Text -->
                            <materialDesign:Card Background="{DynamicResource MaterialDesignCardBackground}"
                                               Padding="16"
                                               Margin="0,16,0,0">
                                <StackPanel>
                                    <DockPanel Margin="0,0,0,8">
                                        <materialDesign:PackIcon Kind="HelpCircle"
                                                               Width="20" Height="20"
                                                               VerticalAlignment="Center"
                                                               Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                        <TextBlock Text="How to get your API key"
                                                 Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                                 VerticalAlignment="Center"
                                                 Margin="8,0,0,0"/>
                                    </DockPanel>

                                    <TextBlock TextWrapping="Wrap"
                                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                                             Opacity="0.8">
                                        <Run Text="1. Visit "/>
                                        <Hyperlink NavigateUri="https://openrouter.ai/keys"
                                                 RequestNavigate="Hyperlink_RequestNavigate">
                                            <Run Text="openrouter.ai/keys"/>
                                        </Hyperlink>
                                        <LineBreak/>
                                        <Run Text="2. Sign up or log in to your account"/>
                                        <LineBreak/>
                                        <Run Text="3. Create a new API key"/>
                                        <LineBreak/>
                                        <Run Text="4. Copy and paste it above"/>
                                    </TextBlock>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>
            </ScrollViewer>

            <!-- Footer Buttons -->
            <Border Grid.Row="2"
                   Background="{DynamicResource MaterialDesignDivider}"
                   Padding="24,16">
                <StackPanel Orientation="Horizontal"
                           HorizontalAlignment="Right">
                    <Button Content="Save"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Command="{Binding SaveSettingsCommand}"
                           Margin="0,0,8,0">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ContentSave"
                                                           Width="16" Height="16"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,4,0"/>
                                    <TextBlock Text="Save" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                    <Button Content="Close"
                           Style="{StaticResource MaterialDesignFlatButton}"
                           Click="CloseButton_Click"/>
                </StackPanel>
            </Border>
        </Grid>
    </materialDesign:DialogHost>
</Window>
