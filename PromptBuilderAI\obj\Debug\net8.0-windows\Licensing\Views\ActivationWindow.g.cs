﻿#pragma checksum "..\..\..\..\..\Licensing\Views\ActivationWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E52079CCCD4F2CCB7087B2F02CE907196CE10245"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace PromptBuilderAI.Licensing.Views {
    
    
    /// <summary>
    /// ActivationWindow
    /// </summary>
    public partial class ActivationWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 57 "..\..\..\..\..\Licensing\Views\ActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SerialPart1;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\..\Licensing\Views\ActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SerialPart2;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\..\Licensing\Views\ActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SerialPart3;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\..\Licensing\Views\ActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SerialPart4;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\..\Licensing\Views\ActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card StatusCard;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\..\Licensing\Views\ActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Licensing\Views\ActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusDetails;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\..\Licensing\Views\ActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ActivateButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.1.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/PromptBuilderAI;component/licensing/views/activationwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Licensing\Views\ActivationWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.1.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SerialPart1 = ((System.Windows.Controls.TextBox)(target));
            
            #line 61 "..\..\..\..\..\Licensing\Views\ActivationWindow.xaml"
            this.SerialPart1.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SerialPart_TextChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SerialPart2 = ((System.Windows.Controls.TextBox)(target));
            
            #line 69 "..\..\..\..\..\Licensing\Views\ActivationWindow.xaml"
            this.SerialPart2.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SerialPart_TextChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SerialPart3 = ((System.Windows.Controls.TextBox)(target));
            
            #line 77 "..\..\..\..\..\Licensing\Views\ActivationWindow.xaml"
            this.SerialPart3.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SerialPart_TextChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SerialPart4 = ((System.Windows.Controls.TextBox)(target));
            
            #line 85 "..\..\..\..\..\Licensing\Views\ActivationWindow.xaml"
            this.SerialPart4.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SerialPart_TextChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.StatusCard = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            case 6:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.StatusDetails = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.ActivateButton = ((System.Windows.Controls.Button)(target));
            
            #line 103 "..\..\..\..\..\Licensing\Views\ActivationWindow.xaml"
            this.ActivateButton.Click += new System.Windows.RoutedEventHandler(this.ActivateButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 110 "..\..\..\..\..\Licensing\Views\ActivationWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.StartTrialButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 115 "..\..\..\..\..\Licensing\Views\ActivationWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BuyLicenseButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 170 "..\..\..\..\..\Licensing\Views\ActivationWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

