using System;
using System.Reflection;
using System.Windows.Controls;
using System.Windows.Threading;

namespace PromptBuilderAI.Views
{
    /// <summary>
    /// Loading Splash UserControl for Prompt Builder AI
    /// </summary>
    public partial class LoadingSplash : UserControl
    {
        private readonly DispatcherTimer _progressTimer;
        private int _progressValue = 0;
        private readonly string[] _loadingMessages = {
            "Initializing...",
            "Loading components...",
            "Setting up AI services...",
            "Preparing interface...",
            "Almost ready..."
        };

        public LoadingSplash()
        {
            InitializeComponent();
            
            // Set version from assembly
            SetVersionInfo();
            
            // Initialize timer
            _progressTimer = new DispatcherTimer();
            
            // Start loading when loaded
            Loaded += LoadingSplash_Loaded;
        }

        private void LoadingSplash_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            // Start progress simulation
            StartProgressSimulation();
        }

        private void SetVersionInfo()
        {
            try
            {
                var version = Assembly.GetExecutingAssembly().GetName().Version;
                VersionText.Text = $"{version.Major}.{version.Minor}.{version.Build}";
            }
            catch
            {
                VersionText.Text = "1.0.0";
            }
        }

        private void StartProgressSimulation()
        {
            _progressTimer.Interval = TimeSpan.FromMilliseconds(100);
            _progressTimer.Tick += ProgressTimer_Tick;
            _progressTimer.Start();
        }

        private void ProgressTimer_Tick(object sender, EventArgs e)
        {
            _progressValue += 3;
            
            if (_progressValue <= 100)
            {
                ProgressBar.Value = _progressValue;
                
                // Update loading message based on progress
                int messageIndex = Math.Min(_progressValue / 20, _loadingMessages.Length - 1);
                LoadingText.Text = _loadingMessages[messageIndex];
            }
            else
            {
                _progressTimer.Stop();
                LoadingText.Text = "Ready!";
                
                // Notify completion
                OnLoadingCompleted?.Invoke();
            }
        }

        /// <summary>
        /// Event fired when loading is completed
        /// </summary>
        public Action OnLoadingCompleted { get; set; }

        /// <summary>
        /// Update loading message manually
        /// </summary>
        public void UpdateLoadingMessage(string message)
        {
            Dispatcher.Invoke(() =>
            {
                LoadingText.Text = message;
            });
        }

        /// <summary>
        /// Update progress manually
        /// </summary>
        public void UpdateProgress(int value)
        {
            Dispatcher.Invoke(() =>
            {
                ProgressBar.Value = Math.Min(Math.Max(value, 0), 100);
            });
        }

        /// <summary>
        /// Stop the loading animation
        /// </summary>
        public void StopLoading()
        {
            _progressTimer?.Stop();
        }
    }
}
