#nullable enable
using System;
using System.ComponentModel;
namespace PromptBuilderAI.Models
{
	public class ProgrammingLanguage : INotifyPropertyChanged
	{
		public string Name { get; set; } = string.Empty;

		private bool _isSelected;
		public bool IsSelected
		{
			get => _isSelected;
			set
			{
				_isSelected = value;
				PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(IsSelected)));
			}
		}

		public event PropertyChangedEventHandler? PropertyChanged;
	}
}
