<Window x:Class="SerialGeneratorTool.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:viewmodels="clr-namespace:SerialGeneratorTool.ViewModels"
        Title="Serial Generator Tool - Prompt Builder AI" 
        Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.DataContext>
        <viewmodels:MainViewModel/>
    </Window.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,20" Padding="20">
            <StackPanel>
                <TextBlock Text="🔑 Serial Generator Tool" 
                          FontSize="24" FontWeight="Bold" 
                          HorizontalAlignment="Center"
                          Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                <TextBlock Text="Generate and manage license serial numbers for Prompt Builder AI" 
                          FontSize="14" 
                          HorizontalAlignment="Center"
                          Margin="0,5,0,0"
                          Opacity="0.7"/>
            </StackPanel>
        </materialDesign:Card>        <!-- License Type Selection -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,20" Padding="20">
            <StackPanel>
                <TextBlock Text="License Configuration" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,15"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- License Type -->
                    <StackPanel Grid.Column="0" Margin="0,0,20,0">
                        <RadioButton Content="Lifetime License" 
                                   IsChecked="{Binding IsLifetimeLicense}"
                                   FontWeight="SemiBold"
                                   Margin="0,0,0,10"/>
                        <RadioButton Content="Time-Limited License" 
                                   IsChecked="{Binding IsLifetimeLicense, Converter={x:Static materialDesign:NotConverter.Instance}}"
                                   FontWeight="SemiBold"/>
                    </StackPanel>
                    
                    <!-- Duration Settings -->
                    <StackPanel Grid.Column="1" 
                              IsEnabled="{Binding IsLifetimeLicense, Converter={x:Static materialDesign:NotConverter.Instance}}">
                        <TextBlock Text="Duration Presets:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                        <ComboBox ItemsSource="{Binding DurationPresets}"
                                SelectedItem="{Binding SelectedPreset}"
                                materialDesign:HintAssist.Hint="Select Duration"
                                Margin="0,0,0,15">
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding}"/>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBox Grid.Column="0" 
                                   Text="{Binding CustomDays}"
                                   materialDesign:HintAssist.Hint="Days"
                                   Margin="0,0,5,0"/>
                            <TextBox Grid.Column="1" 
                                   Text="{Binding CustomMonths}"
                                   materialDesign:HintAssist.Hint="Months"
                                   Margin="5,0,5,0"/>
                            <TextBox Grid.Column="2" 
                                   Text="{Binding CustomYears}"
                                   materialDesign:HintAssist.Hint="Years"
                                   Margin="5,0,0,0"/>
                        </Grid>
                        
                        <TextBlock Text="{Binding ExpiryDate, StringFormat='Expires: {0:yyyy-MM-dd}'}" 
                                 Margin="0,10,0,0" 
                                 FontSize="12" 
                                 Opacity="0.7"/>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </materialDesign:Card>        <!-- Serial Generation -->
        <materialDesign:Card Grid.Row="2" Margin="0,0,0,20" Padding="20">
            <StackPanel>
                <TextBlock Text="Generated Serial" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,15"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox Grid.Column="0" 
                           Text="{Binding GeneratedSerial}"
                           IsReadOnly="True"
                           FontFamily="Consolas"
                           FontSize="16"
                           FontWeight="Bold"
                           materialDesign:HintAssist.Hint="Generated serial will appear here"
                           Margin="0,0,10,0"/>
                    
                    <Button Grid.Column="1" 
                          Content="Generate"
                          Command="{Binding GenerateSerialCommand}"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Margin="0,0,10,0"
                          Padding="20,10"/>
                    
                    <Button Grid.Column="2" 
                          Content="Copy"
                          Command="{Binding CopyToClipboardCommand}"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Padding="15,10"/>
                </Grid>
                
                <!-- Validation Section -->
                <Separator Margin="0,20"/>
                
                <TextBlock Text="Validate Serial" FontSize="14" FontWeight="SemiBold" Margin="0,15,0,10"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox Grid.Column="0" 
                           Text="{Binding ValidationSerial}"
                           materialDesign:HintAssist.Hint="Enter serial to validate"
                           Margin="0,0,10,0"/>
                    
                    <Button Grid.Column="1" 
                          Content="Validate"
                          Command="{Binding ValidateSerialCommand}"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Padding="15,10"/>
                </Grid>
            </StackPanel>
        </materialDesign:Card>        <!-- Serial Database -->
        <materialDesign:Card Grid.Row="3" Padding="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="Serial Database" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,15"/>
                
                <!-- Action Buttons -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,15">
                    <Button Content="Refresh" 
                          Command="{Binding RefreshSerialDatabaseCommand}"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,10,0"/>
                    <Button Content="Export Unused" 
                          Command="{Binding ExportUnusedSerialsCommand}"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,10,0"/>
                    <Button Content="Clear History" 
                          Command="{Binding ClearHistoryCommand}"
                          Style="{StaticResource MaterialDesignOutlinedButton}"/>
                </StackPanel>
                
                <!-- DataGrid -->
                <DataGrid Grid.Row="2" 
                        ItemsSource="{Binding SerialDatabase}"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        materialDesign:DataGridAssist.CellPadding="8"
                        materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Serial Number" Binding="{Binding Serial}" Width="200"/>
                        <DataGridTextColumn Header="Type" Binding="{Binding Type}" Width="100"/>
                        <DataGridTextColumn Header="Duration" Binding="{Binding Duration}" Width="120"/>
                        <DataGridTextColumn Header="Created" Binding="{Binding CreatedDate, StringFormat=yyyy-MM-dd}" Width="100"/>
                        <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="100"/>
                        <DataGridTextColumn Header="Used Info" Binding="{Binding UsedInfo}" Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Window>