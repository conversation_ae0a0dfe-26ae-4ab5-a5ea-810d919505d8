is_global = true
build_property.ApplicationManifest = app.manifest
build_property.StartupObject = 
build_property.ApplicationDefaultFont = 
build_property.ApplicationHighDpiMode = 
build_property.ApplicationUseCompatibleTextRendering = 
build_property.ApplicationVisualStyles = 
build_property.TargetFramework = net8.0-windows
build_property.TargetPlatformMinVersion = 7.0
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = PromptBuilderAI
build_property.ProjectDir = C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.CsWinRTUseWindowsUIXamlProjections = false
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 
