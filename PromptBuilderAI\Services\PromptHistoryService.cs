using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using LiteDB;
using PromptBuilderAI.Models;

namespace PromptBuilderAI.Services
{
	public class PromptHistoryService : IPromptHistoryService
	{
		public PromptHistoryService()
		{
			string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
			string appFolder = Path.Combine(appDataPath, "PromptBuilderAI");
			Directory.CreateDirectory(appFolder);
			this._databasePath = Path.Combine(appFolder, "prompts.db");
		}
		public async Task SavePromptAsync(PromptHistory prompt)
		{
			await Task.Run(delegate
			{
				using (LiteDatabase db = new LiteDatabase(this._databasePath, null))
				{
					ILiteCollection<PromptHistory> collection = db.GetCollection<PromptHistory>("prompts", BsonAutoId.ObjectId);
					collection.Insert(prompt);
				}
			});
		}
		public async Task<List<PromptHistory>> GetHistoryAsync()
		{
			return await Task.Run<List<PromptHistory>>(delegate
			{
				List<PromptHistory> list;
				using (LiteDatabase db = new LiteDatabase(this._databasePath, null))
				{
					ILiteCollection<PromptHistory> collection = db.GetCollection<PromptHistory>("prompts", BsonAutoId.ObjectId);
					list = (from p in collection.FindAll()
						orderby p.CreatedAt descending
						select p).ToList<PromptHistory>();
				}
				return list;
			});
		}
		public async Task DeletePromptAsync(int id)
		{
			await Task.Run(delegate
			{
				using (LiteDatabase db = new LiteDatabase(this._databasePath, null))
				{
					ILiteCollection<PromptHistory> collection = db.GetCollection<PromptHistory>("prompts", BsonAutoId.ObjectId);
					collection.Delete(id);
				}
			});
		}
		public async Task ClearHistoryAsync()
		{
			await Task.Run(delegate
			{
				using (LiteDatabase db = new LiteDatabase(this._databasePath, null))
				{
					ILiteCollection<PromptHistory> collection = db.GetCollection<PromptHistory>("prompts", BsonAutoId.ObjectId);
					collection.DeleteAll();
				}
			});
		}
		private readonly string _databasePath;
	}
}
