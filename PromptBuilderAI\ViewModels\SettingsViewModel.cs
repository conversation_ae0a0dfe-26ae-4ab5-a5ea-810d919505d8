using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PromptBuilderAI.Models;
using PromptBuilderAI.Services;

namespace PromptBuilderAI.ViewModels
{
    /// <summary>
    /// ViewModel for the Settings window - OpenRouter API Configuration
    /// </summary>
    public partial class SettingsViewModel : ObservableObject
    {
        private readonly ISecureStorageService _secureStorage;
        private readonly IOpenRouterService _openRouterService;

        [ObservableProperty]
        private string _apiKey = string.Empty;

        [ObservableProperty]
        private string _selectedModel = "openai/gpt-4";

        [ObservableProperty]
        private bool _isLoadingModels = false;

        [ObservableProperty]
        private bool _isTestingConnection = false;

        [ObservableProperty]
        private string _statusMessage = string.Empty;

        [ObservableProperty]
        private bool _hasValidApiKey = false;

        [ObservableProperty]
        private bool _hasValidModel = false;

        [ObservableProperty]
        private string _apiKeyStatus = string.Empty;

        [ObservableProperty]
        private string _modelStatus = string.Empty;

        [ObservableProperty]
        private string _connectionDetails = string.Empty;

        [ObservableProperty]
        private bool _isValidatingApiKey = false;

        [ObservableProperty]
        private bool _isValidatingModel = false;

        [ObservableProperty]
        private string _modelSearchText = string.Empty;

        [ObservableProperty]
        private bool _isSearchingModels = false;

        public ObservableCollection<OpenRouterModel> AvailableModels { get; } = new();
        public ObservableCollection<OpenRouterModel> FilteredModels { get; } = new();
        private List<OpenRouterModel> _allModels = new();

        // Property for binding to show count
        public int FilteredModelsCount => FilteredModels.Count;

        public SettingsViewModel(ISecureStorageService secureStorage, IOpenRouterService openRouterService)
        {
            _secureStorage = secureStorage;
            _openRouterService = openRouterService;

            // Subscribe to collection changes to update count
            FilteredModels.CollectionChanged += (s, e) => OnPropertyChanged(nameof(FilteredModelsCount));

            // Load saved settings
            _ = LoadSettingsAsync();
        }

        partial void OnModelSearchTextChanged(string value)
        {
            _ = FilterModelsAsync();
        }

        private async Task LoadSettingsAsync()
        {
            try
            {
                // Load API key from secure storage
                var savedApiKey = await _secureStorage.GetSecureValueAsync("OpenRouter_ApiKey");
                if (!string.IsNullOrWhiteSpace(savedApiKey))
                {
                    ApiKey = savedApiKey;
                    HasValidApiKey = true;
                }

                // Load selected model from settings
                var savedModel = Properties.Settings.Default.SelectedModel;
                if (!string.IsNullOrWhiteSpace(savedModel))
                {
                    SelectedModel = savedModel;
                }

                // Load available models
                await LoadAvailableModelsAsync();
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading settings: {ex.Message}";
            }
        }

        [RelayCommand]
        private async Task LoadAvailableModels()
        {
            await LoadAvailableModelsAsync();
        }

        private async Task LoadAvailableModelsAsync()
        {
            if (string.IsNullOrWhiteSpace(ApiKey))
            {
                // Load default models
                LoadDefaultModels();
                return;
            }

            IsLoadingModels = true;
            StatusMessage = "Loading available models...";

            try
            {
                var models = await _openRouterService.GetAvailableModelsAsync(ApiKey);

                // Store all models for filtering
                _allModels = models.OrderBy(m => m.Name).ToList();

                AvailableModels.Clear();
                foreach (var model in _allModels)
                {
                    AvailableModels.Add(model);
                }

                // Initialize filtered models
                await FilterModelsAsync();

                StatusMessage = $"Loaded {models.Count} models successfully.";
                HasValidApiKey = true;
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading models: {ex.Message}";
                LoadDefaultModels();
                HasValidApiKey = false;
            }
            finally
            {
                IsLoadingModels = false;
            }
        }

        private void LoadDefaultModels()
        {
            var defaultModels = _openRouterService.GetDefaultModels();

            _allModels = defaultModels.Select(modelId => new OpenRouterModel
            {
                Id = modelId,
                Name = GetFriendlyModelName(modelId),
                Description = $"Default model: {GetFriendlyModelName(modelId)}",
                ContextLength = GetModelContextLength(modelId)
            }).OrderBy(m => m.Name).ToList();

            AvailableModels.Clear();
            foreach (var model in _allModels)
            {
                AvailableModels.Add(model);
            }

            // Initialize filtered models
            _ = FilterModelsAsync();
        }

        /// <summary>
        /// Filters models based on search text with smart matching
        /// </summary>
        private async Task FilterModelsAsync()
        {
            if (string.IsNullOrWhiteSpace(ModelSearchText))
            {
                // Show all models if no search text
                FilteredModels.Clear();
                foreach (var model in _allModels)
                {
                    FilteredModels.Add(model);
                }
                return;
            }

            IsSearchingModels = true;

            try
            {
                // Use the existing search functionality from OpenRouterService
                var searchResults = await _openRouterService.SearchModelsAsync(ApiKey, ModelSearchText);

                // If API search fails or no API key, do local search
                if (!searchResults.Any() || string.IsNullOrWhiteSpace(ApiKey))
                {
                    searchResults = _allModels.Where(m =>
                        (m.Name?.Contains(ModelSearchText, StringComparison.OrdinalIgnoreCase) == true) ||
                        (m.Id?.Contains(ModelSearchText, StringComparison.OrdinalIgnoreCase) == true) ||
                        (m.Description?.Contains(ModelSearchText, StringComparison.OrdinalIgnoreCase) == true)
                    ).ToList();
                }

                FilteredModels.Clear();
                foreach (var model in searchResults.OrderBy(m => m.Name))
                {
                    FilteredModels.Add(model);
                }
            }
            catch (Exception)
            {
                // Fallback to local search on error
                var localResults = _allModels.Where(m =>
                    (m.Name?.Contains(ModelSearchText, StringComparison.OrdinalIgnoreCase) == true) ||
                    (m.Id?.Contains(ModelSearchText, StringComparison.OrdinalIgnoreCase) == true) ||
                    (m.Description?.Contains(ModelSearchText, StringComparison.OrdinalIgnoreCase) == true)
                ).ToList();

                FilteredModels.Clear();
                foreach (var model in localResults.OrderBy(m => m.Name))
                {
                    FilteredModels.Add(model);
                }
            }
            finally
            {
                IsSearchingModels = false;
            }
        }

        [RelayCommand]
        private void ClearModelSearch()
        {
            ModelSearchText = string.Empty;
        }

        [RelayCommand]
        private async Task ValidateApiKey()
        {
            if (string.IsNullOrWhiteSpace(ApiKey))
            {
                ApiKeyStatus = "⚠️ Please enter an API key";
                HasValidApiKey = false;
                return;
            }

            IsValidatingApiKey = true;
            ApiKeyStatus = "🔄 Validating API key...";

            try
            {
                var result = await _openRouterService.ValidateApiKeyAsync(ApiKey);

                HasValidApiKey = result.IsValid;
                ApiKeyStatus = result.Message;

                if (result.IsValid)
                {
                    // Update connection details
                    var details = $"Models Available: {result.AvailableModelsCount}";
                    if (result.AccountBalance.HasValue)
                    {
                        details += $"\nBalance: ${result.AccountBalance:F4}";
                    }
                    if (!string.IsNullOrEmpty(result.AccountType))
                    {
                        details += $"\nAccount: {result.AccountType}";
                    }
                    ConnectionDetails = details;

                    // Auto-load models after successful validation
                    await LoadAvailableModelsAsync();
                }
                else
                {
                    ConnectionDetails = string.Empty;
                    HasValidModel = false;
                    ModelStatus = string.Empty;
                }
            }
            catch (Exception ex)
            {
                ApiKeyStatus = $"❌ Validation error: {ex.Message}";
                HasValidApiKey = false;
                ConnectionDetails = string.Empty;
            }
            finally
            {
                IsValidatingApiKey = false;
            }
        }

        [RelayCommand]
        private async Task ValidateModel()
        {
            if (string.IsNullOrWhiteSpace(SelectedModel))
            {
                ModelStatus = "⚠️ Please select a model";
                HasValidModel = false;
                return;
            }

            if (!HasValidApiKey)
            {
                ModelStatus = "⚠️ Please validate API key first";
                HasValidModel = false;
                return;
            }

            IsValidatingModel = true;
            ModelStatus = "🔄 Validating model...";

            try
            {
                var result = await _openRouterService.ValidateModelAsync(ApiKey, SelectedModel);

                HasValidModel = result.IsValid;
                ModelStatus = result.Message;

                if (result.IsValid)
                {
                    var details = $"Context Length: {result.ContextLength:N0} tokens";
                    if (!string.IsNullOrEmpty(result.PricingInfo))
                    {
                        details += $"\nPricing: {result.PricingInfo}";
                    }
                    ModelStatus += $"\n{details}";
                }
            }
            catch (Exception ex)
            {
                ModelStatus = $"❌ Model validation error: {ex.Message}";
                HasValidModel = false;
            }
            finally
            {
                IsValidatingModel = false;
            }
        }

        [RelayCommand]
        private async Task TestConnection()
        {
            if (string.IsNullOrWhiteSpace(ApiKey))
            {
                StatusMessage = "Please enter an API key first.";
                return;
            }

            if (string.IsNullOrWhiteSpace(SelectedModel))
            {
                StatusMessage = "Please select a model first.";
                return;
            }

            IsTestingConnection = true;
            StatusMessage = "🔄 Testing full connection (API + Model + Actual call)...";

            try
            {
                var result = await _openRouterService.TestFullConnectionAsync(ApiKey, SelectedModel);

                StatusMessage = result.Message;

                if (result.IsSuccessful)
                {
                    HasValidApiKey = result.ApiResult.IsValid;
                    HasValidModel = result.ModelResult.IsValid;

                    ApiKeyStatus = result.ApiResult.Message;
                    ModelStatus = result.ModelResult.Message;

                    // Update connection details with comprehensive info
                    ConnectionDetails = $"✅ Full Test Completed\n" +
                                      $"Response Time: {result.ResponseTimeMs}ms\n" +
                                      $"Models Available: {result.ApiResult.AvailableModelsCount}\n" +
                                      $"Model Context: {result.ModelResult.ContextLength:N0} tokens";

                    if (result.ApiResult.AccountBalance.HasValue)
                    {
                        ConnectionDetails += $"\nAccount Balance: ${result.ApiResult.AccountBalance:F4}";
                    }
                }
                else
                {
                    HasValidApiKey = result.ApiResult.IsValid;
                    HasValidModel = result.ModelResult.IsValid;
                    ApiKeyStatus = result.ApiResult.Message;
                    ModelStatus = result.ModelResult.Message;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"❌ Connection test failed: {ex.Message}";
                HasValidApiKey = false;
                HasValidModel = false;
            }
            finally
            {
                IsTestingConnection = false;
            }
        }

        [RelayCommand]
        private async Task SaveSettings()
        {
            try
            {
                // Save API key securely
                if (!string.IsNullOrWhiteSpace(ApiKey))
                {
                    await _secureStorage.StoreSecureValueAsync("OpenRouter_ApiKey", ApiKey);
                }
                else
                {
                    await _secureStorage.RemoveSecureValueAsync("OpenRouter_ApiKey");
                }

                // Save selected model to settings
                Properties.Settings.Default.SelectedModel = SelectedModel;
                Properties.Settings.Default.Save();

                StatusMessage = "✅ Settings saved successfully!";
            }
            catch (Exception ex)
            {
                StatusMessage = $"❌ Error saving settings: {ex.Message}";
            }
        }

        [RelayCommand]
        private async Task ClearApiKey()
        {
            try
            {
                await _secureStorage.RemoveSecureValueAsync("OpenRouter_ApiKey");
                ApiKey = string.Empty;
                HasValidApiKey = false;
                StatusMessage = "API key cleared.";

                // Load default models
                LoadDefaultModels();
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error clearing API key: {ex.Message}";
            }
        }

        private static string GetFriendlyModelName(string modelId)
        {
            return modelId switch
            {
                // === LATEST & MOST POPULAR ===
                "deepseek/deepseek-r1-0528" => "🔥 DeepSeek R1 0528",
                "deepseek/deepseek-r1-0528:free" => "🆓 DeepSeek R1 0528 (Free)",
                "anthropic/claude-opus-4" => "🚀 Claude Opus 4",
                "anthropic/claude-sonnet-4" => "🚀 Claude Sonnet 4",
                "google/gemini-2.5-pro-preview" => "🚀 Gemini 2.5 Pro Preview",
                "google/gemini-2.5-flash-preview-05-20" => "⚡ Gemini 2.5 Flash Preview",
                "mistralai/devstral-small" => "💻 Devstral Small",
                "mistralai/mistral-medium-3" => "🔥 Mistral Medium 3",

                // === OPENAI MODELS ===
                "openai/gpt-4o" => "GPT-4o",
                "openai/gpt-4-turbo" => "GPT-4 Turbo",
                "openai/gpt-4" => "GPT-4",
                "openai/gpt-3.5-turbo" => "GPT-3.5 Turbo",
                "openai/codex-mini" => "Codex Mini",

                // === ANTHROPIC CLAUDE ===
                "anthropic/claude-3.5-sonnet" => "Claude 3.5 Sonnet",
                "anthropic/claude-3-opus" => "Claude 3 Opus",
                "anthropic/claude-3-sonnet" => "Claude 3 Sonnet",
                "anthropic/claude-3-haiku" => "Claude 3 Haiku",
                "anthropic/claude-2.1" => "Claude 2.1",

                // === GOOGLE GEMINI ===
                "google/gemini-pro-1.5" => "Gemini Pro 1.5",
                "google/gemini-pro" => "Gemini Pro",
                "google/gemma-3n-e4b-it:free" => "🆓 Gemma 3n 4B (Free)",
                "google/gemma-2b-it" => "Gemma 2B",

                // === MISTRAL AI ===
                "mistralai/mistral-large" => "Mistral Large",
                "mistralai/mistral-medium" => "Mistral Medium",
                "mistralai/mistral-small" => "Mistral Small",
                "mistralai/mixtral-8x7b-instruct" => "Mixtral 8x7B",
                "mistralai/mistral-7b-instruct-v0.2" => "Mistral 7B v0.2",
                "mistralai/mistral-tiny" => "Mistral Tiny",

                // === META LLAMA ===
                "meta-llama/llama-3.3-8b-instruct:free" => "🆓 Llama 3.3 8B (Free)",
                "meta-llama/llama-3.1-405b-instruct" => "Llama 3.1 405B",
                "meta-llama/llama-3.1-70b-instruct" => "Llama 3.1 70B",
                "meta-llama/llama-2-70b-chat" => "Llama 2 70B Chat",

                // === SPECIALIZED MODELS ===
                "cohere/command-r-plus" => "Command R+",
                "arcee-ai/maestro-reasoning" => "🧠 Maestro Reasoning",
                "arcee-ai/caller-large" => "📞 Caller Large",
                "arcee-ai/spotlight" => "🔍 Spotlight Vision",
                "sarvamai/sarvam-m" => "🇮🇳 Sarvam-M (Multilingual)",

                // === COMMUNITY & FINE-TUNED ===
                "nousresearch/deephermes-3-mistral-24b-preview:free" => "🆓 DeepHermes 3 24B (Free)",
                "nousresearch/nous-hermes-2-mixtral-8x7b-dpo" => "Nous Hermes 2 Mixtral",
                "thedrummer/valkyrie-49b-v1" => "⚔️ Valkyrie 49B",
                "gryphe/mythomax-l2-13b" => "📚 MythoMax 13B",
                "neversleep/noromaid-20b" => "🌙 Noromaid 20B",
                "alpindale/goliath-120b" => "🏔️ Goliath 120B",

                // === UTILITY ===
                "openrouter/auto" => "🤖 Auto Router (Smart)",

                _ => modelId
            };
        }

        private static int GetModelContextLength(string modelId)
        {
            return modelId switch
            {
                // === LATEST & MOST POPULAR ===
                "deepseek/deepseek-r1-0528" => 128000,
                "deepseek/deepseek-r1-0528:free" => 163840,
                "anthropic/claude-opus-4" => 200000,
                "anthropic/claude-sonnet-4" => 200000,
                "google/gemini-2.5-pro-preview" => 1048576,
                "google/gemini-2.5-flash-preview-05-20" => 1048576,
                "mistralai/devstral-small" => 128000,
                "mistralai/mistral-medium-3" => 131072,

                // === OPENAI MODELS ===
                "openai/gpt-4o" => 128000,
                "openai/gpt-4-turbo" => 128000,
                "openai/gpt-4" => 8192,
                "openai/gpt-3.5-turbo" => 16385,
                "openai/codex-mini" => 200000,

                // === ANTHROPIC CLAUDE ===
                "anthropic/claude-3.5-sonnet" => 200000,
                "anthropic/claude-3-opus" => 200000,
                "anthropic/claude-3-sonnet" => 200000,
                "anthropic/claude-3-haiku" => 200000,
                "anthropic/claude-2.1" => 200000,

                // === GOOGLE GEMINI ===
                "google/gemini-pro-1.5" => 2000000,
                "google/gemini-pro" => 32768,
                "google/gemma-3n-e4b-it:free" => 8192,
                "google/gemma-2b-it" => 8192,

                // === MISTRAL AI ===
                "mistralai/mistral-large" => 128000,
                "mistralai/mistral-medium" => 32768,
                "mistralai/mistral-small" => 32768,
                "mistralai/mixtral-8x7b-instruct" => 32768,
                "mistralai/mistral-7b-instruct-v0.2" => 32768,
                "mistralai/mistral-tiny" => 32768,

                // === META LLAMA ===
                "meta-llama/llama-3.3-8b-instruct:free" => 128000,
                "meta-llama/llama-3.1-405b-instruct" => 131072,
                "meta-llama/llama-3.1-70b-instruct" => 131072,
                "meta-llama/llama-2-70b-chat" => 4096,

                // === SPECIALIZED MODELS ===
                "cohere/command-r-plus" => 128000,
                "arcee-ai/maestro-reasoning" => 128000,
                "arcee-ai/caller-large" => 32768,
                "arcee-ai/spotlight" => 131072,
                "sarvamai/sarvam-m" => 32768,

                // === COMMUNITY & FINE-TUNED ===
                "nousresearch/deephermes-3-mistral-24b-preview:free" => 32768,
                "nousresearch/nous-hermes-2-mixtral-8x7b-dpo" => 32768,
                "thedrummer/valkyrie-49b-v1" => 131072,
                "gryphe/mythomax-l2-13b" => 4096,
                "neversleep/noromaid-20b" => 8192,
                "alpindale/goliath-120b" => 6144,

                // === UTILITY ===
                "openrouter/auto" => 2000000, // Dynamic based on routed model

                _ => 4096
            };
        }
    }
}
