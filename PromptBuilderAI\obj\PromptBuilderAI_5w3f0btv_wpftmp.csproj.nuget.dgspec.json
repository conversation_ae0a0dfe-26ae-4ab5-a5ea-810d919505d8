{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\PromptBuilderAI\\PromptBuilderAI\\PromptBuilderAI.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\PromptBuilderAI\\PromptBuilderAI\\PromptBuilderAI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\PromptBuilderAI\\PromptBuilderAI\\PromptBuilderAI.csproj", "projectName": "PromptBuilderAI", "projectPath": "C:\\Users\\<USER>\\Desktop\\PromptBuilderAI\\PromptBuilderAI\\PromptBuilderAI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\PromptBuilderAI\\PromptBuilderAI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "LiteDB": {"target": "Package", "version": "[5.0.17, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "OpenAI": {"target": "Package", "version": "[1.11.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}}}