using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;

namespace PromptBuilderAI.Services
{
    /// <summary>
    /// Service to handle rate limiting for API calls
    /// </summary>
    public class RateLimitService
    {
        private readonly ConcurrentDictionary<string, RateLimitInfo> _rateLimits = new();

        /// <summary>
        /// Waits if necessary to respect rate limits before making an API call
        /// </summary>
        /// <param name="apiKey">The API key (used as identifier)</param>
        /// <param name="requestsPerMinute">Maximum requests per minute (default: 20)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        public async Task WaitForRateLimitAsync(string apiKey, int requestsPerMinute = 20, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(apiKey))
                return;

            var rateLimitKey = GetRateLimitKey(apiKey);
            var rateLimitInfo = _rateLimits.GetOrAdd(rateLimitKey, _ => new RateLimitInfo());

            await rateLimitInfo.WaitForNextRequestAsync(requestsPerMinute, cancellationToken);
        }

        /// <summary>
        /// Records that an API call was made (for tracking purposes)
        /// </summary>
        /// <param name="apiKey">The API key</param>
        public void RecordRequest(string apiKey)
        {
            if (string.IsNullOrWhiteSpace(apiKey))
                return;

            var rateLimitKey = GetRateLimitKey(apiKey);
            var rateLimitInfo = _rateLimits.GetOrAdd(rateLimitKey, _ => new RateLimitInfo());
            rateLimitInfo.RecordRequest();
        }

        private static string GetRateLimitKey(string apiKey)
        {
            // Use a hash of the API key for privacy
            return $"rate_limit_{apiKey.GetHashCode():X}";
        }

        private class RateLimitInfo
        {
            private readonly object _lock = new();
            private DateTime _windowStart = DateTime.UtcNow;
            private int _requestCount = 0;

            public Task WaitForNextRequestAsync(int requestsPerMinute, CancellationToken cancellationToken)
            {
                lock (_lock)
                {
                    var now = DateTime.UtcNow;
                    var windowDuration = TimeSpan.FromMinutes(1);

                    // Reset window if it's been more than a minute
                    if (now - _windowStart >= windowDuration)
                    {
                        _windowStart = now;
                        _requestCount = 0;
                    }

                    // Check if we're at the limit
                    if (_requestCount >= requestsPerMinute)
                    {
                        var waitTime = windowDuration - (now - _windowStart);
                        if (waitTime > TimeSpan.Zero)
                        {
                            // Return a delay task
                            return Task.Delay(waitTime, cancellationToken).ContinueWith(_ => {
                                lock (_lock)
                                {
                                    _windowStart = DateTime.UtcNow;
                                    _requestCount = 0;
                                }
                            }, cancellationToken);
                        }
                    }
                }
                return Task.CompletedTask;
            }

            public void RecordRequest()
            {
                lock (_lock)
                {
                    _requestCount++;
                }
            }
        }
    }
}
