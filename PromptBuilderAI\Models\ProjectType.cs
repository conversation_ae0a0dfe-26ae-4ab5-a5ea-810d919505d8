using System;

namespace PromptBuilderAI.Models
{
	public enum ProjectType
	{
		MobileApp,
		DesktopApp,
		WebApp,
		CmdScript,
		BatScript
	}

	/// <summary>
	/// Defines the priority level for prompt requirements
	/// </summary>
	public enum PriorityLevel
	{
		Critical = 1,
		High = 2,
		Medium = 3,
		Low = 4,
		Optional = 5
	}

	/// <summary>
	/// Defines different prompt structure templates
	/// </summary>
	public enum PromptStructureType
	{
		Standard,
		Detailed,
		Minimal,
		Enterprise,
		Beginner,
		Expert
	}

	/// <summary>
	/// Defines constraint types for the project
	/// </summary>
	public enum ConstraintType
	{
		Technical,
		Business,
		Time,
		Budget,
		Security,
		Performance,
		Accessibility,
		Compliance
	}
}
