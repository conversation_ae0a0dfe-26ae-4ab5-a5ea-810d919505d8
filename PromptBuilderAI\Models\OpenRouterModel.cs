using System;
namespace PromptBuilderAI.Models
{
	public class OpenRouterModel
	{
		public string Id { get; set; } = string.Empty;
		public string Name { get; set; } = string.Empty;
		public string Description { get; set; } = string.Empty;
		public int ContextLength { get; set; }
		public ModelPricing Pricing
		{
			get;
			set;
		}
		public string DisplayName
		{
			get
			{
				return (!string.IsNullOrEmpty(this.Name)) ? this.Name : this.Id;
			}
		}
	}
}
