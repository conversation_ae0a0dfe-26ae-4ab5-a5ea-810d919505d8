#nullable enable
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PromptBuilderAI.Models;
using PromptBuilderAI.Properties;
using PromptBuilderAI.Services;
using PromptBuilderAI.Views;
using PromptBuilderAI.Licensing.Services;
using PromptBuilderAI.Licensing.Views;
using MaterialDesignThemes.Wpf;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http;

namespace PromptBuilderAI.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly IPromptGeneratorService _promptGeneratorService;
        private readonly IPromptHistoryService _historyService;
        private readonly IOpenRouterService? _openRouterService;
        private readonly ISecureStorageService? _secureStorage;
        private readonly LicenseManager _licenseManager;

        public ObservableCollection<ProjectTypeInfo> ProjectTypes { get; } = new ObservableCollection<ProjectTypeInfo>();
        public ObservableCollection<PromptHistory> History { get; } = new ObservableCollection<PromptHistory>();
        public ObservableCollection<ProgrammingLanguage> ProgrammingLanguages { get; } = new ObservableCollection<ProgrammingLanguage>();
        public ObservableCollection<Framework> Frameworks { get; } = new ObservableCollection<Framework>();
        public ObservableCollection<ProjectLanguage> ProjectLanguages { get; } = new ObservableCollection<ProjectLanguage>();

        // Collections for all available items
        public ObservableCollection<ProgrammingLanguage> AllProgrammingLanguages { get; } = new ObservableCollection<ProgrammingLanguage>();
        public ObservableCollection<Framework> AllFrameworks { get; } = new ObservableCollection<Framework>();
        public ObservableCollection<Library> AllLibraries { get; } = new ObservableCollection<Library>();

        // Filtered collections for UI binding
        public ObservableCollection<Library> Libraries { get; } = new ObservableCollection<Library>();
        public ObservableCollection<Library> DesignLibraries { get; } = new ObservableCollection<Library>();

        // Grouped libraries by category
        public ObservableCollection<LibraryGroup> LibraryGroups { get; } = new ObservableCollection<LibraryGroup>();

        // Search functionality
        private string _librarySearchText = string.Empty;
        public string LibrarySearchText
        {
            get => _librarySearchText;
            set
            {
                _librarySearchText = value;
                OnPropertyChanged();
                FilterLibrariesBySearch();
            }
        }

        // Add properties for ComboBox binding
        public ObservableCollection<ProjectTypeInfo> ProjectTypeValues => ProjectTypes;
        public Array ThemeSupportValues => Enum.GetValues(typeof(ThemeSupport));
        public Array ColorPaletteValues => Enum.GetValues(typeof(ColorPalette));

        [ObservableProperty]
        private string projectIdea = string.Empty;

        [ObservableProperty]
        private ProjectTypeInfo? selectedProjectType;

        [ObservableProperty]
        private string generatedPrompt = string.Empty;

        [ObservableProperty]
        private string enhancedPrompt = string.Empty;

        [ObservableProperty]
        private bool isGenerating = false;

        [ObservableProperty]
        private bool isEnhancing = false;

        [ObservableProperty]
        private string statusMessage = string.Empty;

        [ObservableProperty]
        private string aiEnhancementStatus = "AI enhancement feature is currently disabled.";

        [ObservableProperty]
        private bool isAiEnhancementAvailable = false;

        [ObservableProperty]
        private ThemeSupport? selectedThemeSupport;

        [ObservableProperty]
        private ColorPalette? selectedColorPalette;

        [ObservableProperty]
        private bool showThemeOptions = false;

        [ObservableProperty]
        private bool showColorPaletteOptions = false;

        [ObservableProperty]
        private bool isColorSelectionEnabled = true;

        [ObservableProperty]
        private Library? selectedDesignLibrary;

        [ObservableProperty]
        private bool showDesignLibrarySelection = false;

        // New properties for structured prompt
        [ObservableProperty]
        private PromptStructureType selectedStructureType = PromptStructureType.Standard;

        [ObservableProperty]
        private StructuredPromptConfig currentPromptConfig = new StructuredPromptConfig();

        [ObservableProperty]
        private bool useStructuredPrompt = false;

        [ObservableProperty]
        private string structuredPrompt = string.Empty;

        [ObservableProperty]
        private bool showAdvancedOptions = false;

        // Collections for structured prompt components
        public ObservableCollection<ProjectRequirement> Requirements { get; } = new ObservableCollection<ProjectRequirement>();
        public ObservableCollection<ProjectConstraint> Constraints { get; } = new ObservableCollection<ProjectConstraint>();
        public ObservableCollection<SuccessCriteria> SuccessCriteria { get; } = new ObservableCollection<SuccessCriteria>();
        public ObservableCollection<PromptSection> CustomSections { get; } = new ObservableCollection<PromptSection>();

        // Enum values for ComboBox binding
        public Array PromptStructureTypeValues => Enum.GetValues(typeof(PromptStructureType));
        public Array PriorityLevelValues => Enum.GetValues(typeof(PriorityLevel));
        public Array ConstraintTypeValues => Enum.GetValues(typeof(ConstraintType));

        // Simple color properties
        [ObservableProperty]
        private System.Windows.Media.Color primaryColor = System.Windows.Media.Color.FromRgb(0x67, 0x3A, 0xB7); // Material Deep Purple

        [ObservableProperty]
        private System.Windows.Media.Color backgroundColor = System.Windows.Media.Color.FromRgb(0xF3, 0xE5, 0xF5); // Light Purple

        [ObservableProperty]
        private System.Windows.Media.Color accentColor = System.Windows.Media.Color.FromRgb(0xFF, 0x57, 0x22); // Material Orange

        // Hex representations for display
        public string PrimaryColorHex => $"#{PrimaryColor.R:X2}{PrimaryColor.G:X2}{PrimaryColor.B:X2}";
        public string BackgroundColorHex => $"#{BackgroundColor.R:X2}{BackgroundColor.G:X2}{BackgroundColor.B:X2}";
        public string AccentColorHex => $"#{AccentColor.R:X2}{AccentColor.G:X2}{AccentColor.B:X2}";

        // License properties
        [ObservableProperty]
        private bool isLicensed = false;

        [ObservableProperty]
        private string licenseStatus = "Not Activated";

        [ObservableProperty]
        private string licenseInfo = "";

        // Feature availability properties
        public bool CanUseAIEnhancement => _licenseManager?.IsFeatureEnabled("AIEnhancement") ?? false;
        public bool CanUseAdvancedTemplates => _licenseManager?.IsFeatureEnabled("AdvancedTemplates") ?? false;
        public bool CanUseExportOptions => _licenseManager?.IsFeatureEnabled("ExportOptions") ?? false;

        public MainViewModel(IPromptGeneratorService promptGeneratorService, IPromptHistoryService historyService)
        {
            _promptGeneratorService = promptGeneratorService;
            _historyService = historyService;
            _licenseManager = new LicenseManager();
            InitializeData();

            // Initialize with default structure
            LoadDefaultStructure();

            // Check license status
            CheckLicenseStatus();
        }

        // Constructor for DI with OpenRouter service
        public MainViewModel(IPromptGeneratorService promptGeneratorService, IPromptHistoryService historyService, IOpenRouterService openRouterService, ISecureStorageService secureStorage)
        {
            _promptGeneratorService = promptGeneratorService;
            _historyService = historyService;
            _openRouterService = openRouterService;
            _secureStorage = secureStorage;
            _licenseManager = new LicenseManager();
            InitializeData();
            
            // Check license status
            CheckLicenseStatus();
        }

        private void InitializeData()
        {
            // Initialize project types with their compatible languages and frameworks
            ProjectTypes.Add(new ProjectTypeInfo
            {
                Type = ProjectType.WebApp,
                DisplayName = "Web Application",
                SupportsThemes = true,
                SupportsColorPalette = true,
                AvailableLanguages = new List<string> { "JavaScript", "TypeScript", "HTML", "CSS", "SCSS", "SASS", "Less", "C#", "Python", "PHP", "Ruby", "Java", "Go" },
                AvailableFrameworks = new List<string> { "React", "Vue.js", "Angular", "Next.js", "Nuxt.js", "Svelte", "ASP.NET Core", "Blazor", "Django", "Flask", "FastAPI", "Spring Boot", "Express.js", "Node.js", "Bootstrap", "Tailwind CSS", "Material-UI" },
                LanguageFrameworks = new Dictionary<string, List<string>>
                {
                    { "JavaScript", new List<string> { "React", "Vue.js", "Angular", "Next.js", "Svelte", "Express.js", "Node.js" } },
                    { "TypeScript", new List<string> { "React", "Vue.js", "Angular", "Next.js", "Svelte", "Express.js", "Node.js" } },
                    { "C#", new List<string> { "ASP.NET Core", "Blazor" } },
                    { "Python", new List<string> { "Django", "Flask", "FastAPI" } },
                    { "Java", new List<string> { "Spring Boot" } },
                    { "CSS", new List<string> { "Bootstrap", "Tailwind CSS" } },
                    { "HTML", new List<string> { "Bootstrap", "Tailwind CSS", "Material-UI" } }
                }
            });
            ProjectTypes.Add(new ProjectTypeInfo
            {
                Type = ProjectType.MobileApp,
                DisplayName = "Mobile Application",
                SupportsThemes = true,
                SupportsColorPalette = true,
                AvailableLanguages = new List<string> { "C#", "JavaScript", "TypeScript", "Dart", "Swift", "Kotlin", "Java" },
                AvailableFrameworks = new List<string> { "React Native", "Flutter", "MAUI", "Xamarin", "Ionic", "Cordova" },
                LanguageFrameworks = new Dictionary<string, List<string>>
                {
                    { "C#", new List<string> { "MAUI", "Xamarin" } },
                    { "JavaScript", new List<string> { "React Native", "Ionic", "Cordova" } },
                    { "TypeScript", new List<string> { "React Native", "Ionic" } },
                    { "Dart", new List<string> { "Flutter" } },
                    { "Swift", new List<string> { } }, // Native iOS
                    { "Kotlin", new List<string> { } }, // Native Android
                    { "Java", new List<string> { } } // Native Android
                }
            });
            ProjectTypes.Add(new ProjectTypeInfo
            {
                Type = ProjectType.DesktopApp,
                DisplayName = "Desktop Application",
                SupportsThemes = true,
                SupportsColorPalette = true,
                AvailableLanguages = new List<string> { "C#", "C++", "C", "Python", "Java", "JavaScript", "TypeScript", "Rust", "Go" },
                AvailableFrameworks = new List<string> { "WPF", "WinUI", "MAUI", "Electron", "Tauri", "Qt", "GTK", "Tkinter", "PyQt", "Swing", "JavaFX" },
                LanguageFrameworks = new Dictionary<string, List<string>>
                {
                    { "C#", new List<string> { "WPF", "WinUI", "MAUI" } },
                    { "C++", new List<string> { "Qt", "GTK" } },
                    { "C", new List<string> { "GTK" } },
                    { "Python", new List<string> { "Tkinter", "PyQt" } },
                    { "Java", new List<string> { "Swing", "JavaFX" } },
                    { "JavaScript", new List<string> { "Electron" } },
                    { "TypeScript", new List<string> { "Electron" } },
                    { "Rust", new List<string> { "Tauri" } }
                }
            });
            ProjectTypes.Add(new ProjectTypeInfo
            {
                Type = ProjectType.CmdScript,
                DisplayName = "Command Script",
                SupportsThemes = false,
                SupportsColorPalette = false,
                AvailableLanguages = new List<string> { "Python", "C#", "JavaScript", "Go", "Rust", "C++", "C", "Java" },
                AvailableFrameworks = new List<string> { "Node.js", ".NET Core", "Spring Boot" }
            });
            ProjectTypes.Add(new ProjectTypeInfo
            {
                Type = ProjectType.BatScript,
                DisplayName = "Batch Script",
                SupportsThemes = false,
                SupportsColorPalette = false,
                AvailableLanguages = new List<string> { "Batch", "PowerShell", "Python", "C#" },
                AvailableFrameworks = new List<string> { ".NET Core", "PowerShell Core" }
            });

            // Initialize Programming Languages
            InitializeProgrammingLanguages();

            // Initialize Frameworks
            InitializeFrameworks();

            // Initialize Project Languages
            InitializeProjectLanguages();

            // Initialize Libraries
            InitializeLibraries();

            // Set defaults
            SelectedProjectType = ProjectTypes.First();

            // Initialize filtered collections based on default project type
            if (SelectedProjectType != null)
            {
                UpdateFilteredProgrammingLanguages(SelectedProjectType);
                UpdateFilteredFrameworks(SelectedProjectType);
            }

            // Load history
            _ = LoadHistoryAsync();

            // Load saved settings
            LoadUserSettings();

            // Check AI enhancement availability
            _ = CheckAiEnhancementAvailabilityAsync();
        }

        /// <summary>
        /// Loads user settings from Properties.Settings
        /// </summary>
        private void LoadUserSettings()
        {
            try
            {
                // Load color selection setting
                IsColorSelectionEnabled = Properties.Settings.Default.IsColorSelectionEnabled;
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading user settings: {ex.Message}";
                // Use default values if loading fails
                IsColorSelectionEnabled = true;
            }
        }

        /// <summary>
        /// Saves user settings to Properties.Settings
        /// </summary>
        private void SaveUserSettings()
        {
            try
            {
                Properties.Settings.Default.IsColorSelectionEnabled = IsColorSelectionEnabled;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error saving user settings: {ex.Message}";
            }
        }

        /// <summary>
        /// Checks if AI enhancement is available and updates status
        /// </summary>
        private async Task CheckAiEnhancementAvailabilityAsync()
        {
            try
            {
                if (_openRouterService != null && _secureStorage != null)
                {
                    // Get API key from secure storage
                    var apiKey = await _secureStorage.GetSecureValueAsync("OpenRouter_ApiKey");

                    if (!string.IsNullOrWhiteSpace(apiKey))
                    {
                        // Validate API key
                        var validationResult = await _openRouterService.ValidateApiKeyAsync(apiKey);

                        if (validationResult.IsValid)
                        {
                            IsAiEnhancementAvailable = true;
                            AiEnhancementStatus = $"✅ AI Enhancement Ready! ({validationResult.AvailableModelsCount} models available)";
                        }
                        else
                        {
                            IsAiEnhancementAvailable = false;
                            AiEnhancementStatus = $"❌ {validationResult.Message}";
                        }
                    }
                    else
                    {
                        IsAiEnhancementAvailable = false;
                        AiEnhancementStatus = "⚙️ Configure OpenRouter API key in Settings to enable AI enhancement.";
                    }
                }
                else
                {
                    IsAiEnhancementAvailable = false;
                    AiEnhancementStatus = "❌ AI enhancement service not available.";
                }
            }
            catch (Exception ex)
            {
                IsAiEnhancementAvailable = false;
                AiEnhancementStatus = $"⚠️ Error checking AI enhancement: {ex.Message}";
            }
        }

        [RelayCommand]
        private async Task RefreshAiStatus()
        {
            await CheckAiEnhancementAvailabilityAsync();
        }

        [RelayCommand]
        private async Task GeneratePrompt()
        {
            if (string.IsNullOrWhiteSpace(ProjectIdea) || SelectedProjectType == null)
            {
                StatusMessage = "Please enter a project idea and select a project type.";
                return;
            }

            IsGenerating = true;
            StatusMessage = "Generating prompt...";

            try
            {
                var selectedLanguages = ProgrammingLanguages.Where(l => l.IsSelected).ToList();
                var selectedFrameworks = Frameworks.Where(f => f.IsSelected).ToList();
                var selectedProjectLanguages = ProjectLanguages.Where(pl => pl.IsSelected).ToList();

                // Add selected design library to frameworks if chosen
                if (SelectedDesignLibrary != null)
                {
                    selectedFrameworks.Add(new Framework
                    {
                        Name = $"{SelectedDesignLibrary.Name} ({SelectedDesignLibrary.Category})",
                        IsSelected = true
                    });
                }

                var prompt = _promptGeneratorService.GeneratePrompt(
                    ProjectIdea,
                    SelectedProjectType.Type,
                    selectedLanguages,
                    selectedFrameworks,
                    selectedProjectLanguages,
                    SelectedThemeSupport,
                    IsColorSelectionEnabled ? SelectedColorPalette : null);

                GeneratedPrompt = prompt;
                StatusMessage = "Prompt generated successfully!";

                // Auto-save if enabled
                if (Settings.Default.AutoSavePrompts)
                {
                    await SaveToHistory();
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error generating prompt: {ex.Message}";
            }
            finally
            {
                IsGenerating = false;
            }
        }

        [RelayCommand]
        private async Task EnhancePrompt()
        {
            // Check license first
            if (!CheckFeatureAccess("AIEnhancement"))
            {
                return;
            }

            if (string.IsNullOrWhiteSpace(GeneratedPrompt))
            {
                StatusMessage = "Please generate a prompt first.";
                return;
            }

            IsEnhancing = true;
            StatusMessage = "Enhancing prompt with AI...";

            try
            {
                if (_openRouterService != null && _secureStorage != null)
                {
                    // Get API key from secure storage
                    var apiKey = await _secureStorage.GetSecureValueAsync("OpenRouter_ApiKey");

                    if (!string.IsNullOrWhiteSpace(apiKey))
                    {
                        // Use AI enhancement
                        var enhancedPrompt = await _openRouterService.EnhancePromptAsync(
                            GeneratedPrompt,
                            apiKey,
                            Settings.Default.SelectedModel ?? "openai/gpt-4");

                        EnhancedPrompt = enhancedPrompt;
                        StatusMessage = "Prompt enhanced successfully with AI!";
                    }
                    else
                    {
                        StatusMessage = "Please configure your OpenRouter API key in Settings.";
                    }
                }
                else
                {
                    // Fallback to local enhancement
                    EnhancedPrompt = $"Enhanced version:\n\n{GeneratedPrompt}\n\nAdditional considerations:\n- Follow best practices\n- Include error handling\n- Add comprehensive documentation\n- Consider scalability and performance\n- Implement proper testing\n- Add security measures\n- Optimize for performance";
                    StatusMessage = "Prompt enhanced successfully! (Configure API key for AI enhancement)";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error enhancing prompt: {ex.Message}";
                // Fallback to local enhancement on error
                EnhancedPrompt = $"Enhanced version:\n\n{GeneratedPrompt}\n\nAdditional considerations:\n- Follow best practices\n- Include error handling\n- Add comprehensive documentation\n- Consider scalability and performance";
            }
            finally
            {
                IsEnhancing = false;
            }
        }

        [RelayCommand]
        private void CopyPrompt(string promptType)
        {
            try
            {
                string textToCopy = promptType switch
                {
                    "generated" => GeneratedPrompt,
                    "enhanced" => EnhancedPrompt,
                    _ => GeneratedPrompt
                };

                if (!string.IsNullOrWhiteSpace(textToCopy))
                {
                    Clipboard.SetText(textToCopy);
                    StatusMessage = "Prompt copied to clipboard!";
                }
                else
                {
                    StatusMessage = "No prompt to copy.";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error copying to clipboard: {ex.Message}";
            }
        }

        [RelayCommand]
        private async Task OpenSettings()
        {
            try
            {
                StatusMessage = "Opening settings...";

                // Get SettingsViewModel from DI container
                var app = (App)Application.Current;
                if (app?._serviceProvider == null)
                {
                    StatusMessage = "Error: Service provider not initialized.";
                    return;
                }

                var settingsViewModel = app._serviceProvider.GetRequiredService<SettingsViewModel>();
                if (settingsViewModel == null)
                {
                    StatusMessage = "Error: Could not create settings view model.";
                    return;
                }

                var settingsWindow = new SettingsWindow(settingsViewModel)
                {
                    Owner = Application.Current.MainWindow
                };

                StatusMessage = "Settings window opened.";
                settingsWindow.ShowDialog();

                // Refresh AI status after settings window closes
                await CheckAiEnhancementAvailabilityAsync();
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error opening settings: {ex.Message}";

                // Fallback: Create services manually
                try
                {
                    StatusMessage = "Trying fallback method...";
                    var secureStorage = new SecureStorageService();
                    var httpClient = new System.Net.Http.HttpClient();
                    var openRouterService = new OpenRouterService(httpClient);
                    var settingsViewModel = new SettingsViewModel(secureStorage, openRouterService);

                    var fallbackWindow = new SettingsWindow(settingsViewModel)
                    {
                        Owner = Application.Current.MainWindow
                    };

                    StatusMessage = "Settings opened (fallback mode).";
                    fallbackWindow.ShowDialog();
                }
                catch (Exception fallbackEx)
                {
                    StatusMessage = $"Fallback failed: {fallbackEx.Message}. Trying basic window...";

                    // Last resort: Basic window
                    try
                    {
                        var basicWindow = new SettingsWindow()
                        {
                            Owner = Application.Current.MainWindow
                        };

                        StatusMessage = "Basic settings window opened.";
                        basicWindow.ShowDialog();
                    }
                    catch (Exception basicEx)
                    {
                        StatusMessage = $"Critical error: {basicEx.Message}";
                    }
                }
            }
        }

        [RelayCommand]
        private void ClearForm()
        {
            ProjectIdea = string.Empty;
            GeneratedPrompt = string.Empty;
            EnhancedPrompt = string.Empty;
            SelectedThemeSupport = null;
            SelectedColorPalette = null;
            ShowThemeOptions = false;
            ShowColorPaletteOptions = false;
            StatusMessage = "Form cleared.";
        }

        [RelayCommand]
        private async Task GenerateStructuredPrompt()
        {
            if (string.IsNullOrWhiteSpace(ProjectIdea) || SelectedProjectType == null)
            {
                StatusMessage = "Please enter a project idea and select a project type.";
                return;
            }

            IsGenerating = true;
            StatusMessage = "Generating structured prompt...";

            try
            {
                var selectedLanguages = ProgrammingLanguages.Where(l => l.IsSelected).ToList();
                var selectedFrameworks = Frameworks.Where(f => f.IsSelected).ToList();
                var selectedProjectLanguages = ProjectLanguages.Where(pl => pl.IsSelected).ToList();

                // Add selected design library to frameworks if chosen
                if (SelectedDesignLibrary != null)
                {
                    selectedFrameworks.Add(new Framework
                    {
                        Name = $"{SelectedDesignLibrary.Name} ({SelectedDesignLibrary.Category})",
                        IsSelected = true
                    });
                }

                // Update current config with collections
                CurrentPromptConfig.Requirements = Requirements.ToList();
                CurrentPromptConfig.Constraints = Constraints.ToList();
                CurrentPromptConfig.SuccessCriteria = SuccessCriteria.ToList();
                CurrentPromptConfig.Sections = CustomSections.ToList();
                CurrentPromptConfig.StructureType = SelectedStructureType;

                var prompt = _promptGeneratorService.GenerateStructuredPrompt(
                    ProjectIdea,
                    SelectedProjectType.Type,
                    CurrentPromptConfig,
                    selectedLanguages,
                    selectedFrameworks,
                    selectedProjectLanguages,
                    SelectedThemeSupport,
                    IsColorSelectionEnabled ? SelectedColorPalette : null);

                StructuredPrompt = prompt;
                StatusMessage = "Structured prompt generated successfully!";

                // Auto-save if enabled
                if (Settings.Default.AutoSavePrompts)
                {
                    await SaveToHistory();
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error generating structured prompt: {ex.Message}";
            }
            finally
            {
                IsGenerating = false;
            }
        }

        [RelayCommand]
        private void LoadDefaultStructure()
        {
            if (SelectedProjectType == null) return;

            try
            {
                // Load default configuration
                CurrentPromptConfig = _promptGeneratorService.GetDefaultStructureConfig(SelectedStructureType);

                // Load default requirements
                Requirements.Clear();
                var defaultRequirements = _promptGeneratorService.GetDefaultRequirements(SelectedProjectType.Type);
                foreach (var req in defaultRequirements)
                {
                    Requirements.Add(req);
                }

                // Load default constraints
                Constraints.Clear();
                var defaultConstraints = _promptGeneratorService.GetDefaultConstraints(SelectedProjectType.Type);
                foreach (var constraint in defaultConstraints)
                {
                    Constraints.Add(constraint);
                }

                // Load default success criteria
                SuccessCriteria.Clear();
                var defaultCriteria = _promptGeneratorService.GetDefaultSuccessCriteria(SelectedProjectType.Type);
                foreach (var criteria in defaultCriteria)
                {
                    SuccessCriteria.Add(criteria);
                }

                // Load default sections
                CustomSections.Clear();
                var defaultSections = _promptGeneratorService.GetDefaultSections(SelectedProjectType.Type, SelectedStructureType);
                foreach (var section in defaultSections)
                {
                    CustomSections.Add(section);
                }

                StatusMessage = $"Loaded default {SelectedStructureType} structure for {SelectedProjectType.DisplayName}.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading default structure: {ex.Message}";
            }
        }

        [RelayCommand]
        private void AddRequirement()
        {
            Requirements.Add(new ProjectRequirement
            {
                Title = "New Requirement",
                Description = "Enter requirement description...",
                Priority = PriorityLevel.Medium,
                Category = "General"
            });
        }

        [RelayCommand]
        private void RemoveRequirement(ProjectRequirement requirement)
        {
            if (requirement != null)
            {
                Requirements.Remove(requirement);
            }
        }

        [RelayCommand]
        private void AddConstraint()
        {
            Constraints.Add(new ProjectConstraint
            {
                Title = "New Constraint",
                Description = "Enter constraint description...",
                Type = ConstraintType.Technical,
                Impact = PriorityLevel.Medium
            });
        }

        [RelayCommand]
        private void RemoveConstraint(ProjectConstraint constraint)
        {
            if (constraint != null)
            {
                Constraints.Remove(constraint);
            }
        }

        [RelayCommand]
        private void AddSuccessCriteria()
        {
            SuccessCriteria.Add(new SuccessCriteria
            {
                Title = "New Success Criteria",
                Description = "Enter success criteria description...",
                Priority = PriorityLevel.Medium
            });
        }

        [RelayCommand]
        private void RemoveSuccessCriteria(SuccessCriteria criteria)
        {
            if (criteria != null)
            {
                SuccessCriteria.Remove(criteria);
            }
        }

        [RelayCommand]
        private void AddCustomSection()
        {
            CustomSections.Add(new PromptSection
            {
                Title = "New Section",
                Content = "Enter section content...",
                Priority = PriorityLevel.Medium,
                Order = CustomSections.Count + 1
            });
        }

        [RelayCommand]
        private void RemoveCustomSection(PromptSection section)
        {
            if (section != null)
            {
                CustomSections.Remove(section);
            }
        }

        [RelayCommand]
        private void ToggleAdvancedOptions()
        {
            ShowAdvancedOptions = !ShowAdvancedOptions;
            StatusMessage = ShowAdvancedOptions ? "Advanced options shown." : "Advanced options hidden.";
        }

        partial void OnIsColorSelectionEnabledChanged(bool value)
        {
            StatusMessage = value ? "🎨 Color selection enabled." : "🚫 Color selection disabled.";

            // Clear color selection when disabled
            if (!value)
            {
                SelectedColorPalette = null;
            }

            // Save the setting
            SaveUserSettings();
        }

        [RelayCommand]
        private void SelectPrimaryColor()
        {
            var color = ShowColorDialog(PrimaryColor);
            if (color.HasValue)
            {
                PrimaryColor = color.Value;
                OnPropertyChanged(nameof(PrimaryColorHex));
                StatusMessage = $"🎨 Primary color updated to {PrimaryColorHex}";
            }
        }

        [RelayCommand]
        private void SelectBackgroundColor()
        {
            var color = ShowColorDialog(BackgroundColor);
            if (color.HasValue)
            {
                BackgroundColor = color.Value;
                OnPropertyChanged(nameof(BackgroundColorHex));
                StatusMessage = $"🏠 Background color updated to {BackgroundColorHex}";
            }
        }

        [RelayCommand]
        private void SelectAccentColor()
        {
            var color = ShowColorDialog(AccentColor);
            if (color.HasValue)
            {
                AccentColor = color.Value;
                OnPropertyChanged(nameof(AccentColorHex));
                StatusMessage = $"⭐ Accent color updated to {AccentColorHex}";
            }
        }





        [RelayCommand]
        private void ToggleTheme()
        {
            try
            {
                var paletteHelper = new PaletteHelper();
                var theme = paletteHelper.GetTheme();

                if (theme.GetBaseTheme() == BaseTheme.Dark)
                {
                    // Switch to Light
                    theme.SetBaseTheme(Theme.Light);
                    Properties.Settings.Default.IsDarkMode = false;
                    StatusMessage = "☀️ Switched to Light Mode";
                }
                else
                {
                    // Switch to Dark with custom color #181818
                    theme.SetBaseTheme(Theme.Dark);

                    // Apply custom dark background color
                    theme.Paper = System.Windows.Media.Color.FromRgb(0x18, 0x18, 0x18);
                    theme.CardBackground = System.Windows.Media.Color.FromRgb(0x20, 0x20, 0x20);
                    theme.ToolBarBackground = System.Windows.Media.Color.FromRgb(0x25, 0x25, 0x25);

                    Properties.Settings.Default.IsDarkMode = true;
                    StatusMessage = "🌙 Switched to Dark Mode (#181818)";
                }

                paletteHelper.SetTheme(theme);

                // Save settings
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error switching theme: {ex.Message}";
            }
        }

        [RelayCommand]
        private async Task ClearAll()
        {
            // Ask for confirmation using Material Design dialog
            var result = await ShowMaterialConfirmDialog(
                "Clear All Data",
                "Are you sure you want to clear all data? This will reset everything to start fresh.",
                "Clear All",
                "Cancel");

            if (!result)
            {
                return;
            }

            // Clear all form data
            ProjectIdea = string.Empty;
            GeneratedPrompt = string.Empty;
            EnhancedPrompt = string.Empty;
            SelectedThemeSupport = null;
            SelectedColorPalette = null;
            ShowThemeOptions = false;
            ShowColorPaletteOptions = false;

            // Reset project type to default
            SelectedProjectType = ProjectTypes.FirstOrDefault();

            // Clear all selections
            foreach (var lang in ProgrammingLanguages)
            {
                lang.IsSelected = false;
            }

            foreach (var framework in Frameworks)
            {
                framework.IsSelected = false;
            }

            foreach (var projLang in ProjectLanguages)
            {
                projLang.IsSelected = false;
            }

            // Clear library search
            LibrarySearchText = string.Empty;

            StatusMessage = "✅ All data cleared successfully! Ready for new project.";
        }

        [RelayCommand]
        private async Task DeleteHistoryItem(PromptHistory historyItem)
        {
            if (historyItem == null) return;

            // Ask for confirmation using Material Design dialog
            var result = await ShowMaterialConfirmDialog(
                "Delete History Item",
                $"Are you sure you want to delete this history item?\n\n\"{historyItem.ProjectIdea}\"",
                "Delete",
                "Cancel");

            if (!result) return;

            try
            {
                // Remove from service
                await _historyService.DeletePromptAsync(historyItem.Id);

                // Remove from UI collection
                History.Remove(historyItem);

                StatusMessage = "✅ History item deleted successfully.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"❌ Error deleting history item: {ex.Message}";
            }
        }

        private async Task<bool> ShowMaterialConfirmDialog(string title, string message, string confirmText, string cancelText)
        {
            var dialogContent = new StackPanel
            {
                Margin = new Thickness(24),
                MinWidth = 320
            };

            // Icon
            var iconPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 16)
            };

            var icon = new PackIcon
            {
                Kind = PackIconKind.HelpCircle,
                Width = 48,
                Height = 48,
                Foreground = (Brush)Application.Current.FindResource("PrimaryHueMidBrush")
            };
            iconPanel.Children.Add(icon);
            dialogContent.Children.Add(iconPanel);

            // Title
            var titleBlock = new TextBlock
            {
                Text = title,
                FontSize = 20,
                FontWeight = FontWeights.Medium,
                Margin = new Thickness(0, 0, 0, 16),
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center
            };
            dialogContent.Children.Add(titleBlock);

            // Message
            var messageBlock = new TextBlock
            {
                Text = message,
                FontSize = 14,
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 24),
                Opacity = 0.8,
                TextAlignment = TextAlignment.Center,
                LineHeight = 20
            };
            dialogContent.Children.Add(messageBlock);

            // Buttons
            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 8, 0, 0)
            };

            var cancelButton = new Button
            {
                Content = cancelText,
                Margin = new Thickness(0, 0, 12, 0),
                Padding = new Thickness(24, 8, 24, 8),
                MinWidth = 100
            };

            var confirmButton = new Button
            {
                Content = confirmText,
                Padding = new Thickness(24, 8, 24, 8),
                MinWidth = 100,
                Background = (Brush)Application.Current.FindResource("MaterialDesignValidationErrorBrush")
            };

            // Apply styles
            try
            {
                cancelButton.Style = (Style)Application.Current.FindResource("MaterialDesignFlatButton");
                confirmButton.Style = (Style)Application.Current.FindResource("MaterialDesignRaisedButton");
            }
            catch
            {
                // Fallback if styles not found
            }

            buttonPanel.Children.Add(cancelButton);
            buttonPanel.Children.Add(confirmButton);
            dialogContent.Children.Add(buttonPanel);

            bool? result = null;

            cancelButton.Click += (s, e) => {
                result = false;
                DialogHost.CloseDialogCommand.Execute(false, null);
            };

            confirmButton.Click += (s, e) => {
                result = true;
                DialogHost.CloseDialogCommand.Execute(true, null);
            };

            await DialogHost.Show(dialogContent, "RootDialog");

            return result ?? false;
        }

        [RelayCommand]
        private void LoadFromHistory(PromptHistory historyItem)
        {
            if (historyItem != null)
            {
                ProjectIdea = historyItem.ProjectIdea;
                var projectType = ProjectTypes.FirstOrDefault(pt => pt.Type == historyItem.ProjectType);
                if (projectType != null)
                {
                    SelectedProjectType = projectType;
                }
                GeneratedPrompt = historyItem.GeneratedPrompt;
                StatusMessage = "Loaded from history.";
            }
        }

        private async Task SaveToHistory()
        {
            try
            {
                var historyItem = new PromptHistory
                {
                    ProjectIdea = ProjectIdea,
                    ProjectType = SelectedProjectType?.Type ?? ProjectType.WebApp,
                    GeneratedPrompt = GeneratedPrompt,
                    EnhancedPrompt = EnhancedPrompt,
                    CreatedAt = DateTime.Now,
                    ThemeSupport = SelectedThemeSupport,
                    ColorPalette = SelectedColorPalette
                };

                await _historyService.SavePromptAsync(historyItem);
                await LoadHistoryAsync(); // Refresh history
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error saving to history: {ex.Message}";
            }
        }

        private async Task LoadHistoryAsync()
        {
            try
            {
                var history = await _historyService.GetHistoryAsync();
                History.Clear();
                foreach (var item in history.Take(10)) // Show last 10 items
                {
                    History.Add(item);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading history: {ex.Message}";
            }
        }

        private void InitializeProgrammingLanguages()
        {
            var languages = new[]
            {
                "C#", "JavaScript", "TypeScript", "Python", "Java", "C++", "C", "Go", "Rust", "Swift",
                "Kotlin", "PHP", "Ruby", "Scala", "F#", "VB.NET", "Dart", "R", "MATLAB", "SQL",
                "HTML", "CSS", "SCSS", "SASS", "Less", "Stylus", "XML", "JSON", "YAML", "TOML",
                "Batch", "PowerShell"
            };

            // Initialize all languages collection
            AllProgrammingLanguages.Clear();
            foreach (var lang in languages)
            {
                AllProgrammingLanguages.Add(new ProgrammingLanguage { Name = lang, IsSelected = false });
            }

            // Initialize filtered collection (will be updated when project type changes)
            ProgrammingLanguages.Clear();
        }

        private void InitializeFrameworks()
        {
            var frameworks = new[]
            {
                // .NET
                "ASP.NET Core", "Blazor", "WPF", "WinUI", "MAUI", "Xamarin", "Entity Framework", ".NET Core",
                // JavaScript/TypeScript
                "React", "Vue.js", "Angular", "Next.js", "Nuxt.js", "Svelte", "Express.js", "Node.js",
                // Python
                "Django", "Flask", "FastAPI", "Pandas", "NumPy", "TensorFlow", "PyTorch", "Tkinter", "PyQt",
                // Java
                "Spring Boot", "Spring Framework", "Hibernate", "Apache Kafka", "Swing", "JavaFX",
                // Mobile
                "React Native", "Flutter", "Ionic", "Cordova",
                // CSS
                "Bootstrap", "Tailwind CSS", "Material-UI", "Ant Design", "Chakra UI",
                // Desktop
                "Electron", "Tauri", "Qt", "GTK",
                // Others
                "Docker", "Kubernetes", "Redis", "MongoDB", "PostgreSQL", "MySQL", "PowerShell Core"
            };

            // Initialize all frameworks collection
            AllFrameworks.Clear();
            foreach (var framework in frameworks)
            {
                AllFrameworks.Add(new Framework { Name = framework, IsSelected = false });
            }

            // Initialize filtered collection (will be updated when project type changes)
            Frameworks.Clear();
        }

        private void InitializeProjectLanguages()
        {
            var projectLanguages = new[]
            {
                new { Name = "English", Code = "en" },
                new { Name = "العربية", Code = "ar" },
                new { Name = "Français", Code = "fr" },
                new { Name = "Español", Code = "es" },
                new { Name = "Deutsch", Code = "de" },
                new { Name = "Italiano", Code = "it" },
                new { Name = "Português", Code = "pt" },
                new { Name = "Русский", Code = "ru" },
                new { Name = "中文", Code = "zh" },
                new { Name = "日本語", Code = "ja" },
                new { Name = "한국어", Code = "ko" },
                new { Name = "हिन्दी", Code = "hi" }
            };

            ProjectLanguages.Clear();
            foreach (var lang in projectLanguages)
            {
                ProjectLanguages.Add(new ProjectLanguage { Name = lang.Name, Code = lang.Code, IsSelected = false });
            }
        }

        private void InitializeLibraries()
        {
            var libraries = new[]
            {
                // WPF UI Libraries
                new Library {
                    Name = "MaterialDesignInXAML",
                    Language = "C#",
                    Framework = "WPF",
                    Category = "UI Framework",
                    Description = "Material Design themes and controls for WPF applications",
                    PackageManager = "NuGet",
                    InstallCommand = "Install-Package MaterialDesignThemes",
                    IconKind = "Palette",
                    Popularity = 5,
                    ImagePath = "",
                    FriendlyName = "Material Design in XAML",
                    UserDescription = "Google Material Design components and themes for WPF"
                },
                new Library {
                    Name = "MahApps.Metro",
                    Language = "C#",
                    Framework = "WPF",
                    Category = "UI Framework",
                    Description = "Metro-style controls and themes for WPF applications",
                    PackageManager = "NuGet",
                    InstallCommand = "Install-Package MahApps.Metro",
                    IconKind = "Metro",
                    Popularity = 5,
                    ImagePath = "",
                    FriendlyName = "MahApps Metro",
                    UserDescription = "Modern Metro design language for WPF applications"
                },
                new Library {
                    Name = "Telerik UI for WPF",
                    Language = "C#",
                    Framework = "WPF",
                    Category = "UI Framework",
                    Description = "Professional WPF controls and components suite",
                    PackageManager = "NuGet",
                    InstallCommand = "Install-Package Telerik.UI.for.Wpf.AllControls",
                    IconKind = "Telerik",
                    Popularity = 5,
                    ImagePath = "",
                    FriendlyName = "Telerik UI for WPF",
                    UserDescription = "Enterprise-grade WPF controls with advanced features"
                },
                new Library {
                    Name = "DevExpress WPF",
                    Language = "C#",
                    Framework = "WPF",
                    Category = "UI Framework",
                    Description = "Comprehensive WPF controls and data visualization components",
                    PackageManager = "NuGet",
                    InstallCommand = "Install-Package DevExpress.Wpf",
                    IconKind = "DevExpress",
                    Popularity = 5,
                    ImagePath = "",
                    FriendlyName = "DevExpress WPF",
                    UserDescription = "Professional WPF controls with rich data visualization"
                },
                new Library {
                    Name = "Syncfusion WPF",
                    Language = "C#",
                    Framework = "WPF",
                    Category = "UI Framework",
                    Description = "Feature-rich WPF controls and components",
                    PackageManager = "NuGet",
                    InstallCommand = "Install-Package Syncfusion.SfSkinManager.WPF",
                    IconKind = "Syncfusion",
                    Popularity = 4,
                    ImagePath = "",
                    FriendlyName = "Syncfusion UI for WPF",
                    UserDescription = "Comprehensive WPF control suite with modern themes"
                },
                new Library {
                    Name = "Avalonia UI",
                    Language = "C#",
                    Framework = "Avalonia",
                    Category = "UI Framework",
                    Description = "Cross-platform .NET UI framework",
                    PackageManager = "NuGet",
                    InstallCommand = "Install-Package Avalonia",
                    IconKind = "Avalonia",
                    Popularity = 4,
                    ImagePath = "",
                    FriendlyName = "Avalonia UI",
                    UserDescription = "Cross-platform XAML-based UI framework for .NET"
                },
                new Library {
                    Name = "ReactiveUI",
                    Language = "C#",
                    Framework = "WPF",
                    Category = "UI Framework",
                    Description = "Reactive programming framework for .NET UI applications",
                    PackageManager = "NuGet",
                    InstallCommand = "Install-Package ReactiveUI.WPF",
                    IconKind = "Reactive",
                    Popularity = 4,
                    ImagePath = "",
                    FriendlyName = "ReactiveUI",
                    UserDescription = "MVVM framework with reactive programming support"
                },
                new Library {
                    Name = "LiveCharts",
                    Language = "C#",
                    Framework = "WPF",
                    Category = "UI Components",
                    Description = "Animated charting library for WPF applications",
                    PackageManager = "NuGet",
                    InstallCommand = "Install-Package LiveCharts.Wpf",
                    IconKind = "Chart",
                    Popularity = 4,
                    ImagePath = "",
                    FriendlyName = "LiveCharts",
                    UserDescription = "Beautiful animated charts and graphs for WPF"
                },
                new Library {
                    Name = "ModernWpf",
                    Language = "C#",
                    Framework = "WPF",
                    Category = "UI Framework",
                    Description = "Modern styles and controls for WPF applications",
                    PackageManager = "NuGet",
                    InstallCommand = "Install-Package ModernWpfUI",
                    IconKind = "Windows",
                    Popularity = 4,
                    ImagePath = "",
                    FriendlyName = "Modern WPF UI",
                    UserDescription = "Windows 11 Fluent Design controls for WPF"
                },

                // WinForms UI Libraries
                new Library {
                    Name = "Telerik UI for WinForms",
                    Language = "C#",
                    Framework = "WinForms",
                    Category = "UI Framework",
                    Description = "Professional WinForms controls and components suite",
                    PackageManager = "NuGet",
                    InstallCommand = "Install-Package Telerik.UI.for.WinForms.AllControls",
                    IconKind = "Telerik",
                    Popularity = 5,
                    ImagePath = "",
                    FriendlyName = "Telerik UI for WinForms",
                    UserDescription = "Enterprise-grade WinForms controls with modern themes"
                },
                new Library {
                    Name = "DevExpress WinForms",
                    Language = "C#",
                    Framework = "WinForms",
                    Category = "UI Framework",
                    Description = "Comprehensive WinForms controls and data visualization",
                    PackageManager = "NuGet",
                    InstallCommand = "Install-Package DevExpress.Win",
                    IconKind = "DevExpress",
                    Popularity = 5,
                    ImagePath = "",
                    FriendlyName = "DevExpress WinForms",
                    UserDescription = "Professional WinForms controls with rich features"
                },
                new Library {
                    Name = "Bunifu UI",
                    Language = "C#",
                    Framework = "WinForms",
                    Category = "UI Framework",
                    Description = "Modern and elegant WinForms controls",
                    PackageManager = "Manual",
                    InstallCommand = "Download from Bunifu website",
                    IconKind = "Bunifu",
                    Popularity = 4,
                    ImagePath = "",
                    FriendlyName = "Bunifu UI Framework",
                    UserDescription = "Beautiful modern controls for WinForms applications"
                },
                new Library {
                    Name = "Guna UI",
                    Language = "C#",
                    Framework = "WinForms",
                    Category = "UI Framework",
                    Description = "Professional WinForms UI controls and components",
                    PackageManager = "Manual",
                    InstallCommand = "Download from Guna website",
                    IconKind = "Guna",
                    Popularity = 4,
                    ImagePath = "",
                    FriendlyName = "Guna UI Framework",
                    UserDescription = "Modern and responsive WinForms controls"
                },
                new Library {
                    Name = "Krypton Toolkit",
                    Language = "C#",
                    Framework = "WinForms",
                    Category = "UI Framework",
                    Description = "Professional WinForms controls with Office-style themes",
                    PackageManager = "NuGet",
                    InstallCommand = "Install-Package Krypton.Toolkit",
                    IconKind = "Krypton",
                    Popularity = 4,
                    ImagePath = "",
                    FriendlyName = "Krypton Toolkit",
                    UserDescription = "Office-style professional controls for WinForms"
                },
                new Library {
                    Name = "Syncfusion WinForms",
                    Language = "C#",
                    Framework = "WinForms",
                    Category = "UI Framework",
                    Description = "Feature-rich WinForms controls and components",
                    PackageManager = "NuGet",
                    InstallCommand = "Install-Package Syncfusion.SfSkinManager.WinForms",
                    IconKind = "Syncfusion",
                    Popularity = 4,
                    ImagePath = "",
                    FriendlyName = "Syncfusion UI for WinForms",
                    UserDescription = "Comprehensive WinForms control suite with themes"
                },
                new Library {
                    Name = "DotNetBar",
                    Language = "C#",
                    Framework = "WinForms",
                    Category = "UI Framework",
                    Description = "Professional WinForms controls with Office-style interface",
                    PackageManager = "Manual",
                    InstallCommand = "Download from DevComponents website",
                    IconKind = "DotNetBar",
                    Popularity = 3,
                    ImagePath = "",
                    FriendlyName = "DotNetBar",
                    UserDescription = "Office-style ribbon and controls for WinForms"
                },
                new Library {
                    Name = "MetroFramework",
                    Language = "C#",
                    Framework = "WinForms",
                    Category = "UI Framework",
                    Description = "Metro-style controls and themes for WinForms",
                    PackageManager = "NuGet",
                    InstallCommand = "Install-Package MetroFramework",
                    IconKind = "Metro",
                    Popularity = 3,
                    ImagePath = "",
                    FriendlyName = "Metro Framework",
                    UserDescription = "Metro design language for WinForms applications"
                },

                // React UI Libraries
                new Library {
                    Name = "Material-UI (MUI)",
                    Language = "JavaScript",
                    Framework = "React",
                    Category = "UI Framework",
                    Description = "React components implementing Google's Material Design",
                    PackageManager = "npm",
                    InstallCommand = "npm install @mui/material @emotion/react @emotion/styled",
                    IconKind = "React",
                    Popularity = 5,
                    ImagePath = "",
                    FriendlyName = "Material-UI",
                    UserDescription = "Complete React component library with Material Design"
                },
                new Library {
                    Name = "Ant Design",
                    Language = "JavaScript",
                    Framework = "React",
                    Category = "UI Framework",
                    Description = "Enterprise-class UI design language and React components",
                    PackageManager = "npm",
                    InstallCommand = "npm install antd",
                    IconKind = "Ant",
                    Popularity = 5,
                    ImagePath = "",
                    FriendlyName = "Ant Design",
                    UserDescription = "Professional React UI library for enterprise applications"
                },
                new Library {
                    Name = "Chakra UI",
                    Language = "JavaScript",
                    Framework = "React",
                    Category = "UI Framework",
                    Description = "Simple, modular and accessible component library for React",
                    PackageManager = "npm",
                    InstallCommand = "npm install @chakra-ui/react @emotion/react @emotion/styled framer-motion",
                    IconKind = "Chakra",
                    Popularity = 4,
                    ImagePath = "",
                    FriendlyName = "Chakra UI",
                    UserDescription = "Simple and accessible React component library"
                },
                new Library {
                    Name = "React Bootstrap",
                    Language = "JavaScript",
                    Framework = "React",
                    Category = "UI Framework",
                    Description = "Bootstrap components built for React",
                    PackageManager = "npm",
                    InstallCommand = "npm install react-bootstrap bootstrap",
                    IconKind = "Bootstrap",
                    Popularity = 4,
                    ImagePath = "",
                    FriendlyName = "React Bootstrap",
                    UserDescription = "Bootstrap components reimplemented for React"
                },
                new Library {
                    Name = "Mantine",
                    Language = "JavaScript",
                    Framework = "React",
                    Category = "UI Framework",
                    Description = "Full-featured React components and hooks library",
                    PackageManager = "npm",
                    InstallCommand = "npm install @mantine/core @mantine/hooks @mantine/notifications",
                    IconKind = "Package",
                    Popularity = 4,
                    ImagePath = "",
                    FriendlyName = "Mantine",
                    UserDescription = "Modern React components library with dark theme support"
                },

                // CSS Frameworks
                new Library {
                    Name = "Bootstrap",
                    Language = "CSS",
                    Framework = "",
                    Category = "CSS Framework",
                    Description = "Popular CSS framework for responsive web design",
                    PackageManager = "npm",
                    InstallCommand = "npm install bootstrap",
                    IconKind = "Bootstrap",
                    Popularity = 5,
                    ImagePath = "",
                    FriendlyName = "Bootstrap",
                    UserDescription = "Complete CSS framework with pre-built components and utilities"
                },
                new Library {
                    Name = "Tailwind CSS",
                    Language = "CSS",
                    Framework = "",
                    Category = "CSS Framework",
                    Description = "Utility-first CSS framework for rapid UI development",
                    PackageManager = "npm",
                    InstallCommand = "npm install tailwindcss",
                    IconKind = "Tailwind",
                    Popularity = 5,
                    ImagePath = "",
                    FriendlyName = "Tailwind CSS",
                    UserDescription = "Utility-first CSS framework for custom designs"
                },
                new Library {
                    Name = "Bulma",
                    Language = "CSS",
                    Framework = "",
                    Category = "CSS Framework",
                    Description = "Modern CSS framework based on Flexbox",
                    PackageManager = "npm",
                    InstallCommand = "npm install bulma",
                    IconKind = "Bulma",
                    Popularity = 4,
                    ImagePath = "",
                    FriendlyName = "Bulma",
                    UserDescription = "Clean and modern CSS framework with no JavaScript"
                },

                // Vue.js UI Libraries
                new Library {
                    Name = "Vuetify",
                    Language = "JavaScript",
                    Framework = "Vue.js",
                    Category = "UI Framework",
                    Description = "Material Design component framework for Vue.js",
                    PackageManager = "npm",
                    InstallCommand = "npm install vuetify",
                    IconKind = "Vuejs",
                    Popularity = 5,
                    ImagePath = "",
                    FriendlyName = "Vuetify",
                    UserDescription = "Complete Material Design component library for Vue.js"
                },
                new Library {
                    Name = "Quasar",
                    Language = "JavaScript",
                    Framework = "Vue.js",
                    Category = "UI Framework",
                    Description = "High-performance Vue.js framework",
                    PackageManager = "npm",
                    InstallCommand = "npm install @quasar/cli",
                    IconKind = "Quasar",
                    Popularity = 4,
                    ImagePath = "",
                    FriendlyName = "Quasar Framework",
                    UserDescription = "Cross-platform Vue.js framework with Material Design"
                },
                new Library {
                    Name = "Element Plus",
                    Language = "JavaScript",
                    Framework = "Vue.js",
                    Category = "UI Framework",
                    Description = "Vue 3 UI library for web applications",
                    PackageManager = "npm",
                    InstallCommand = "npm install element-plus",
                    IconKind = "Element",
                    Popularity = 4,
                    ImagePath = "",
                    FriendlyName = "Element Plus",
                    UserDescription = "Professional Vue 3 component library"
                },

                // Angular UI Libraries
                new Library {
                    Name = "Angular Material",
                    Language = "TypeScript",
                    Framework = "Angular",
                    Category = "UI Framework",
                    Description = "Material Design components for Angular",
                    PackageManager = "npm",
                    InstallCommand = "ng add @angular/material",
                    IconKind = "Angular",
                    Popularity = 5,
                    ImagePath = "",
                    FriendlyName = "Angular Material",
                    UserDescription = "Official Material Design components for Angular"
                },
                new Library {
                    Name = "PrimeNG",
                    Language = "TypeScript",
                    Framework = "Angular",
                    Category = "UI Framework",
                    Description = "Rich set of UI components for Angular",
                    PackageManager = "npm",
                    InstallCommand = "npm install primeng primeicons",
                    IconKind = "Prime",
                    Popularity = 4,
                    ImagePath = "",
                    FriendlyName = "PrimeNG",
                    UserDescription = "Comprehensive Angular UI component suite"
                },
                new Library {
                    Name = "Ng-Zorro",
                    Language = "TypeScript",
                    Framework = "Angular",
                    Category = "UI Framework",
                    Description = "Enterprise-class Angular UI components",
                    PackageManager = "npm",
                    InstallCommand = "ng add ng-zorro-antd",
                    IconKind = "Ant",
                    Popularity = 4,
                    ImagePath = "",
                    FriendlyName = "Ng-Zorro (Ant Design)",
                    UserDescription = "Ant Design components for Angular"
                },

                // Flutter UI Libraries
                new Library {
                    Name = "Flutter Material",
                    Language = "Dart",
                    Framework = "Flutter",
                    Category = "UI Framework",
                    Description = "Material Design widgets for Flutter",
                    PackageManager = "pub",
                    InstallCommand = "flutter pub add material",
                    IconKind = "Flutter",
                    Popularity = 5,
                    ImagePath = "",
                    FriendlyName = "Flutter Material",
                    UserDescription = "Built-in Material Design components for Flutter"
                },
                new Library {
                    Name = "Flutter Cupertino",
                    Language = "Dart",
                    Framework = "Flutter",
                    Category = "UI Framework",
                    Description = "iOS-style widgets for Flutter",
                    PackageManager = "pub",
                    InstallCommand = "flutter pub add cupertino_icons",
                    IconKind = "Apple",
                    Popularity = 4,
                    ImagePath = "",
                    FriendlyName = "Flutter Cupertino",
                    UserDescription = "iOS-style design components for Flutter"
                },

                new Library { Name = "FluentValidation", Language = "C#", Framework = "", Category = "Validation", Description = "Validation library for .NET", PackageManager = "NuGet", InstallCommand = "Install-Package FluentValidation" },
                new Library { Name = "MediatR", Language = "C#", Framework = "", Category = "Architecture", Description = "Mediator pattern implementation", PackageManager = "NuGet", InstallCommand = "Install-Package MediatR" },
                new Library { Name = "Polly", Language = "C#", Framework = "", Category = "Resilience", Description = "Resilience and transient-fault-handling library", PackageManager = "NuGet", InstallCommand = "Install-Package Polly" },
                new Library { Name = "SignalR", Language = "C#", Framework = "ASP.NET Core", Category = "Real-time", Description = "Real-time web functionality", PackageManager = "NuGet", InstallCommand = "Install-Package Microsoft.AspNetCore.SignalR" },
                new Library { Name = "IdentityServer4", Language = "C#", Framework = "ASP.NET Core", Category = "Authentication", Description = "OpenID Connect and OAuth 2.0 framework", PackageManager = "NuGet", InstallCommand = "Install-Package IdentityServer4" },

                // JavaScript / TypeScript Libraries
                new Library { Name = "React", Language = "JavaScript", Framework = "", Category = "UI Framework", Description = "JavaScript library for building user interfaces", PackageManager = "npm", InstallCommand = "npm install react" },
                new Library { Name = "Vue.js", Language = "JavaScript", Framework = "", Category = "UI Framework", Description = "Progressive JavaScript framework", PackageManager = "npm", InstallCommand = "npm install vue" },
                new Library { Name = "Angular", Language = "TypeScript", Framework = "", Category = "UI Framework", Description = "Platform for building mobile and desktop web applications", PackageManager = "npm", InstallCommand = "npm install @angular/core" },
                new Library { Name = "Lodash", Language = "JavaScript", Framework = "", Category = "Utility", Description = "Modern JavaScript utility library", PackageManager = "npm", InstallCommand = "npm install lodash" },
                new Library { Name = "Axios", Language = "JavaScript", Framework = "", Category = "HTTP Client", Description = "Promise based HTTP client", PackageManager = "npm", InstallCommand = "npm install axios" },
                new Library { Name = "Moment.js", Language = "JavaScript", Framework = "", Category = "Date/Time", Description = "Parse, validate, manipulate, and display dates", PackageManager = "npm", InstallCommand = "npm install moment" },
                new Library { Name = "Chart.js", Language = "JavaScript", Framework = "", Category = "Charts", Description = "Simple yet flexible JavaScript charting", PackageManager = "npm", InstallCommand = "npm install chart.js" },
                new Library { Name = "D3.js", Language = "JavaScript", Framework = "", Category = "Data Visualization", Description = "Data-driven documents", PackageManager = "npm", InstallCommand = "npm install d3" },
                new Library { Name = "Three.js", Language = "JavaScript", Framework = "", Category = "3D Graphics", Description = "JavaScript 3D library", PackageManager = "npm", InstallCommand = "npm install three" },
                new Library { Name = "Socket.io", Language = "JavaScript", Framework = "Node.js", Category = "Real-time", Description = "Real-time bidirectional event-based communication", PackageManager = "npm", InstallCommand = "npm install socket.io" },
                new Library { Name = "Express.js", Language = "JavaScript", Framework = "Node.js", Category = "Web Framework", Description = "Fast, unopinionated, minimalist web framework", PackageManager = "npm", InstallCommand = "npm install express" },
                new Library { Name = "Mongoose", Language = "JavaScript", Framework = "Node.js", Category = "Database", Description = "MongoDB object modeling for Node.js", PackageManager = "npm", InstallCommand = "npm install mongoose" },
                new Library { Name = "Passport.js", Language = "JavaScript", Framework = "Node.js", Category = "Authentication", Description = "Simple, unobtrusive authentication for Node.js", PackageManager = "npm", InstallCommand = "npm install passport" },
                new Library { Name = "Jest", Language = "JavaScript", Framework = "", Category = "Testing", Description = "JavaScript testing framework", PackageManager = "npm", InstallCommand = "npm install jest" },
                new Library { Name = "Webpack", Language = "JavaScript", Framework = "", Category = "Build Tool", Description = "Static module bundler", PackageManager = "npm", InstallCommand = "npm install webpack" },
                new Library { Name = "Babel", Language = "JavaScript", Framework = "", Category = "Transpiler", Description = "JavaScript compiler", PackageManager = "npm", InstallCommand = "npm install @babel/core" },

                // React Specific
                new Library { Name = "React Router", Language = "JavaScript", Framework = "React", Category = "Routing", Description = "Declarative routing for React", PackageManager = "npm", InstallCommand = "npm install react-router-dom" },
                new Library { Name = "Redux", Language = "JavaScript", Framework = "React", Category = "State Management", Description = "Predictable state container", PackageManager = "npm", InstallCommand = "npm install redux" },
                new Library { Name = "Material-UI", Language = "JavaScript", Framework = "React", Category = "UI Components", Description = "React components implementing Google's Material Design", PackageManager = "npm", InstallCommand = "npm install @mui/material" },
                new Library { Name = "Ant Design", Language = "JavaScript", Framework = "React", Category = "UI Components", Description = "Enterprise-class UI design language", PackageManager = "npm", InstallCommand = "npm install antd" },
                new Library { Name = "Styled Components", Language = "JavaScript", Framework = "React", Category = "Styling", Description = "CSS-in-JS library for styling React components", PackageManager = "npm", InstallCommand = "npm install styled-components" },

                // Vue Specific
                new Library { Name = "Vuex", Language = "JavaScript", Framework = "Vue.js", Category = "State Management", Description = "State management pattern + library for Vue.js", PackageManager = "npm", InstallCommand = "npm install vuex" },
                new Library { Name = "Vue Router", Language = "JavaScript", Framework = "Vue.js", Category = "Routing", Description = "Official router for Vue.js", PackageManager = "npm", InstallCommand = "npm install vue-router" },
                new Library { Name = "Vuetify", Language = "JavaScript", Framework = "Vue.js", Category = "UI Components", Description = "Material Design component framework for Vue.js", PackageManager = "npm", InstallCommand = "npm install vuetify" },

                // CSS Frameworks
                new Library { Name = "Bootstrap", Language = "CSS", Framework = "", Category = "CSS Framework", Description = "Popular CSS framework", PackageManager = "npm", InstallCommand = "npm install bootstrap" },
                new Library { Name = "Tailwind CSS", Language = "CSS", Framework = "", Category = "CSS Framework", Description = "Utility-first CSS framework", PackageManager = "npm", InstallCommand = "npm install tailwindcss" },
                new Library { Name = "Bulma", Language = "CSS", Framework = "", Category = "CSS Framework", Description = "Modern CSS framework based on Flexbox", PackageManager = "npm", InstallCommand = "npm install bulma" },

                // Python Libraries
                new Library { Name = "Django", Language = "Python", Framework = "", Category = "Web Framework", Description = "High-level Python web framework", PackageManager = "pip", InstallCommand = "pip install django" },
                new Library { Name = "Flask", Language = "Python", Framework = "", Category = "Web Framework", Description = "Lightweight WSGI web application framework", PackageManager = "pip", InstallCommand = "pip install flask" },
                new Library { Name = "FastAPI", Language = "Python", Framework = "", Category = "Web Framework", Description = "Modern, fast web framework for building APIs", PackageManager = "pip", InstallCommand = "pip install fastapi" },
                new Library { Name = "NumPy", Language = "Python", Framework = "", Category = "Scientific Computing", Description = "Fundamental package for scientific computing", PackageManager = "pip", InstallCommand = "pip install numpy" },
                new Library { Name = "Pandas", Language = "Python", Framework = "", Category = "Data Analysis", Description = "Data manipulation and analysis library", PackageManager = "pip", InstallCommand = "pip install pandas" },
                new Library { Name = "Matplotlib", Language = "Python", Framework = "", Category = "Plotting", Description = "Plotting library for Python", PackageManager = "pip", InstallCommand = "pip install matplotlib" },
                new Library { Name = "Seaborn", Language = "Python", Framework = "", Category = "Data Visualization", Description = "Statistical data visualization", PackageManager = "pip", InstallCommand = "pip install seaborn" },
                new Library { Name = "Scikit-learn", Language = "Python", Framework = "", Category = "Machine Learning", Description = "Machine learning library", PackageManager = "pip", InstallCommand = "pip install scikit-learn" },
                new Library { Name = "TensorFlow", Language = "Python", Framework = "", Category = "Deep Learning", Description = "Open source machine learning framework", PackageManager = "pip", InstallCommand = "pip install tensorflow" },
                new Library { Name = "PyTorch", Language = "Python", Framework = "", Category = "Deep Learning", Description = "Deep learning framework", PackageManager = "pip", InstallCommand = "pip install torch" },
                new Library { Name = "Requests", Language = "Python", Framework = "", Category = "HTTP Client", Description = "HTTP library for Python", PackageManager = "pip", InstallCommand = "pip install requests" },
                new Library { Name = "SQLAlchemy", Language = "Python", Framework = "", Category = "Database", Description = "SQL toolkit and ORM", PackageManager = "pip", InstallCommand = "pip install sqlalchemy" },
                new Library { Name = "Celery", Language = "Python", Framework = "", Category = "Task Queue", Description = "Distributed task queue", PackageManager = "pip", InstallCommand = "pip install celery" },
                new Library { Name = "Pytest", Language = "Python", Framework = "", Category = "Testing", Description = "Testing framework", PackageManager = "pip", InstallCommand = "pip install pytest" },

                // Java Libraries
                new Library { Name = "Spring Boot", Language = "Java", Framework = "", Category = "Web Framework", Description = "Java-based framework for building applications", PackageManager = "Maven", InstallCommand = "Add to pom.xml: spring-boot-starter" },
                new Library { Name = "Hibernate", Language = "Java", Framework = "", Category = "ORM", Description = "Object-relational mapping framework", PackageManager = "Maven", InstallCommand = "Add to pom.xml: hibernate-core" },
                new Library { Name = "Jackson", Language = "Java", Framework = "", Category = "JSON", Description = "JSON processing library", PackageManager = "Maven", InstallCommand = "Add to pom.xml: jackson-databind" },
                new Library { Name = "JUnit", Language = "Java", Framework = "", Category = "Testing", Description = "Unit testing framework", PackageManager = "Maven", InstallCommand = "Add to pom.xml: junit" },
                new Library { Name = "Mockito", Language = "Java", Framework = "", Category = "Testing", Description = "Mocking framework", PackageManager = "Maven", InstallCommand = "Add to pom.xml: mockito-core" },
                new Library { Name = "Apache Commons", Language = "Java", Framework = "", Category = "Utility", Description = "Reusable Java components", PackageManager = "Maven", InstallCommand = "Add to pom.xml: commons-lang3" },

                // Mobile Development
                new Library { Name = "React Native Elements", Language = "JavaScript", Framework = "React Native", Category = "UI Components", Description = "Cross-platform UI toolkit", PackageManager = "npm", InstallCommand = "npm install react-native-elements" },
                new Library { Name = "React Navigation", Language = "JavaScript", Framework = "React Native", Category = "Navigation", Description = "Routing and navigation for React Native", PackageManager = "npm", InstallCommand = "npm install @react-navigation/native" },
                new Library { Name = "Flutter Material", Language = "Dart", Framework = "Flutter", Category = "UI Components", Description = "Material Design widgets for Flutter", PackageManager = "pub", InstallCommand = "flutter pub add material" },
                new Library { Name = "Provider", Language = "Dart", Framework = "Flutter", Category = "State Management", Description = "State management solution for Flutter", PackageManager = "pub", InstallCommand = "flutter pub add provider" },

                // Database Libraries
                new Library { Name = "MongoDB Driver", Language = "JavaScript", Framework = "Node.js", Category = "Database", Description = "MongoDB driver for Node.js", PackageManager = "npm", InstallCommand = "npm install mongodb" },
                new Library { Name = "PostgreSQL Driver", Language = "JavaScript", Framework = "Node.js", Category = "Database", Description = "PostgreSQL client for Node.js", PackageManager = "npm", InstallCommand = "npm install pg" },
                new Library { Name = "Redis Client", Language = "JavaScript", Framework = "Node.js", Category = "Database", Description = "Redis client for Node.js", PackageManager = "npm", InstallCommand = "npm install redis" },

                // Testing Libraries
                new Library { Name = "Mocha", Language = "JavaScript", Framework = "", Category = "Testing", Description = "JavaScript test framework", PackageManager = "npm", InstallCommand = "npm install mocha" },
                new Library { Name = "Chai", Language = "JavaScript", Framework = "", Category = "Testing", Description = "BDD / TDD assertion library", PackageManager = "npm", InstallCommand = "npm install chai" },
                new Library { Name = "Cypress", Language = "JavaScript", Framework = "", Category = "E2E Testing", Description = "End-to-end testing framework", PackageManager = "npm", InstallCommand = "npm install cypress" },
                new Library { Name = "Selenium", Language = "Python", Framework = "", Category = "Web Testing", Description = "Web browser automation", PackageManager = "pip", InstallCommand = "pip install selenium" },

                // Build Tools
                new Library { Name = "Vite", Language = "JavaScript", Framework = "", Category = "Build Tool", Description = "Next generation frontend tooling", PackageManager = "npm", InstallCommand = "npm install vite" },
                new Library { Name = "Parcel", Language = "JavaScript", Framework = "", Category = "Build Tool", Description = "Zero configuration build tool", PackageManager = "npm", InstallCommand = "npm install parcel" },
                new Library { Name = "Rollup", Language = "JavaScript", Framework = "", Category = "Build Tool", Description = "Module bundler for JavaScript", PackageManager = "npm", InstallCommand = "npm install rollup" }
            };

            AllLibraries.Clear();
            foreach (var library in libraries)
            {
                AllLibraries.Add(library);
            }

            // Initialize grouped libraries
            UpdateLibraryGroups();
        }

        private void UpdateLibraryGroups()
        {
            LibraryGroups.Clear();

            var categoryIcons = new Dictionary<string, string>
            {
                { "واجهات المستخدم", "Palette" },
                { "قواعد البيانات", "Database" },
                { "UI", "Palette" },
                { "Database", "Database" },
                { "Testing", "TestTube" },
                { "Serialization", "CodeJson" },
                { "Logging", "FileDocument" },
                { "Authentication", "Shield" },
                { "Real-time", "Flash" },
                { "HTTP Client", "Web" },
                { "State Management", "Memory" },
                { "Routing", "Router" },
                { "Charts", "ChartLine" },
                { "Data Visualization", "ChartBar" },
                { "3D Graphics", "Cube" },
                { "Build Tool", "Hammer" },
                { "CSS Framework", "Palette" },
                { "Web Framework", "Web" },
                { "Scientific Computing", "Calculator" },
                { "Machine Learning", "Brain" },
                { "Deep Learning", "Brain" },
                { "Data Analysis", "ChartLine" },
                { "Plotting", "ChartBar" },
                { "Task Queue", "Queue" },
                { "ORM", "Database" },
                { "JSON", "CodeJson" },
                { "Utility", "Tools" },
                { "Navigation", "Navigation" },
                { "UI Components", "ViewGrid" },
                { "Styling", "Palette" },
                { "E2E Testing", "TestTube" },
                { "Web Testing", "Web" },
                { "Transpiler", "Cog" },
                { "UI Framework", "ViewGrid" },
                { "Date/Time", "Calendar" },
                { "Validation", "CheckCircle" },
                { "Architecture", "Sitemap" },
                { "Resilience", "Shield" },
                { "Mapping", "MapMarker" }
            };

            var filteredLibraries = string.IsNullOrWhiteSpace(LibrarySearchText)
                ? Libraries.ToList()
                : Libraries.Where(l => l.Name.Contains(LibrarySearchText, StringComparison.OrdinalIgnoreCase) ||
                                      l.Description.Contains(LibrarySearchText, StringComparison.OrdinalIgnoreCase) ||
                                      l.Category.Contains(LibrarySearchText, StringComparison.OrdinalIgnoreCase)).ToList();

            var groupedLibraries = filteredLibraries
                .GroupBy(l => l.Category)
                .OrderBy(g => g.Key)
                .ToList();

            foreach (var group in groupedLibraries)
            {
                var libraryGroup = new LibraryGroup
                {
                    CategoryName = group.Key,
                    CategoryIcon = categoryIcons.ContainsKey(group.Key) ? categoryIcons[group.Key] : "Package"
                };

                foreach (var library in group.OrderByDescending(l => l.Popularity).ThenBy(l => l.Name))
                {
                    library.ParentGroup = libraryGroup;
                    libraryGroup.Libraries.Add(library);
                }

                LibraryGroups.Add(libraryGroup);
            }
        }

        private void FilterLibrariesBySearch()
        {
            UpdateLibraryGroups();
        }

        partial void OnSelectedProjectTypeChanged(ProjectTypeInfo? value)
        {
            if (value != null)
            {
                ShowThemeOptions = value.Type == ProjectType.WebApp || value.Type == ProjectType.MobileApp || value.Type == ProjectType.DesktopApp;
                ShowColorPaletteOptions = ShowThemeOptions;

                // Update filtered programming languages
                UpdateFilteredProgrammingLanguages(value);

                // Update filtered frameworks
                UpdateFilteredFrameworks(value);

                // Update filtered libraries
                UpdateFilteredLibraries();

                // Update library groups
                UpdateLibraryGroups();
            }
            else
            {
                ShowThemeOptions = false;
                ShowColorPaletteOptions = false;

                // Clear filtered collections
                ProgrammingLanguages.Clear();
                Frameworks.Clear();
                Libraries.Clear();
            }
        }

        private void UpdateFilteredLibraries()
        {
            Libraries.Clear();

            // Get selected languages and frameworks
            var selectedLanguages = ProgrammingLanguages.Where(l => l.IsSelected).Select(l => l.Name).ToList();
            var selectedFrameworks = Frameworks.Where(f => f.IsSelected).Select(f => f.Name).ToList();

            // If no languages or frameworks selected, show libraries for all available languages/frameworks
            if (!selectedLanguages.Any() && !selectedFrameworks.Any())
            {
                var availableLanguages = ProgrammingLanguages.Select(l => l.Name).ToList();
                var availableFrameworks = Frameworks.Select(f => f.Name).ToList();

                foreach (var library in AllLibraries)
                {
                    if (availableLanguages.Contains(library.Language) ||
                        availableFrameworks.Contains(library.Framework) ||
                        string.IsNullOrEmpty(library.Language) && string.IsNullOrEmpty(library.Framework))
                    {
                        Libraries.Add(new Library
                        {
                            Name = library.Name,
                            Language = library.Language,
                            Framework = library.Framework,
                            Category = library.Category,
                            Description = library.Description,
                            PackageManager = library.PackageManager,
                            InstallCommand = library.InstallCommand,
                            IsSelected = false
                        });
                    }
                }
            }
            else
            {
                // Show libraries for selected languages and frameworks
                foreach (var library in AllLibraries)
                {
                    bool matchesLanguage = selectedLanguages.Contains(library.Language) || string.IsNullOrEmpty(library.Language);
                    bool matchesFramework = selectedFrameworks.Contains(library.Framework) || string.IsNullOrEmpty(library.Framework);

                    if (matchesLanguage && matchesFramework)
                    {
                        Libraries.Add(new Library
                        {
                            Name = library.Name,
                            Language = library.Language,
                            Framework = library.Framework,
                            Category = library.Category,
                            Description = library.Description,
                            PackageManager = library.PackageManager,
                            InstallCommand = library.InstallCommand,
                            IsSelected = false
                        });
                    }
                }
            }

            // Update design libraries
            UpdateDesignLibraries();
        }

        private void UpdateDesignLibraries()
        {
            DesignLibraries.Clear();

            // Get selected languages and frameworks
            var selectedLanguages = ProgrammingLanguages.Where(l => l.IsSelected).Select(l => l.Name).ToList();
            var selectedFrameworks = Frameworks.Where(f => f.IsSelected).Select(f => f.Name).ToList();

            // Filter design/UI libraries
            var designCategories = new[] { "UI", "UI Framework", "UI Components", "CSS Framework", "Styling", "Design System" };

            foreach (var library in AllLibraries.Where(l => designCategories.Contains(l.Category)))
            {
                bool matchesLanguage = !selectedLanguages.Any() || selectedLanguages.Contains(library.Language) || string.IsNullOrEmpty(library.Language);
                bool matchesFramework = !selectedFrameworks.Any() || selectedFrameworks.Contains(library.Framework) || string.IsNullOrEmpty(library.Framework);

                if (matchesLanguage && matchesFramework)
                {
                    DesignLibraries.Add(new Library
                    {
                        Name = library.Name,
                        Language = library.Language,
                        Framework = library.Framework,
                        Category = library.Category,
                        Description = library.Description,
                        PackageManager = library.PackageManager,
                        InstallCommand = library.InstallCommand,
                        FriendlyName = library.FriendlyName,
                        UserDescription = library.UserDescription,
                        IsSelected = false
                    });
                }
            }

            // Update visibility
            ShowDesignLibrarySelection = DesignLibraries.Any();
        }

        private void UpdateFilteredProgrammingLanguages(ProjectTypeInfo projectType)
        {
            ProgrammingLanguages.Clear();

            foreach (var availableLanguage in projectType.AvailableLanguages)
            {
                var language = AllProgrammingLanguages.FirstOrDefault(l => l.Name == availableLanguage);
                if (language != null)
                {
                    // Create a new instance to avoid reference issues
                    var newLanguage = new ProgrammingLanguage
                    {
                        Name = language.Name,
                        IsSelected = false
                    };
                    newLanguage.PropertyChanged += (s, e) => {
                        if (e.PropertyName == nameof(ProgrammingLanguage.IsSelected))
                        {
                            UpdateFilteredFrameworksBasedOnLanguage();
                            UpdateFilteredLibraries();
                        }
                    };
                    ProgrammingLanguages.Add(newLanguage);
                }
                else
                {
                    // If language not found in master list, add it anyway
                    var newLanguage = new ProgrammingLanguage
                    {
                        Name = availableLanguage,
                        IsSelected = false
                    };
                    newLanguage.PropertyChanged += (s, e) => {
                        if (e.PropertyName == nameof(ProgrammingLanguage.IsSelected))
                        {
                            UpdateFilteredFrameworksBasedOnLanguage();
                            UpdateFilteredLibraries();
                        }
                    };
                    ProgrammingLanguages.Add(newLanguage);
                }
            }
        }

        private void UpdateFilteredFrameworks(ProjectTypeInfo projectType)
        {
            Frameworks.Clear();

            foreach (var availableFramework in projectType.AvailableFrameworks)
            {
                var framework = AllFrameworks.FirstOrDefault(f => f.Name == availableFramework);
                if (framework != null)
                {
                    // Create a new instance to avoid reference issues
                    var newFramework = new Framework
                    {
                        Name = framework.Name,
                        IsSelected = false
                    };
                    newFramework.PropertyChanged += (s, e) => {
                        if (e.PropertyName == nameof(Framework.IsSelected))
                        {
                            UpdateFilteredLibraries();
                        }
                    };
                    Frameworks.Add(newFramework);
                }
                else
                {
                    // If framework not found in master list, add it anyway
                    var newFramework = new Framework
                    {
                        Name = availableFramework,
                        IsSelected = false
                    };
                    newFramework.PropertyChanged += (s, e) => {
                        if (e.PropertyName == nameof(Framework.IsSelected))
                        {
                            UpdateFilteredLibraries();
                        }
                    };
                    Frameworks.Add(newFramework);
                }
            }
        }

        private void UpdateFilteredFrameworksBasedOnLanguage()
        {
            if (SelectedProjectType == null) return;

            var selectedLanguages = ProgrammingLanguages.Where(l => l.IsSelected).Select(l => l.Name).ToList();

            // If no languages selected, show all frameworks for the project type
            if (!selectedLanguages.Any())
            {
                UpdateFilteredFrameworks(SelectedProjectType);
                return;
            }

            // Show only frameworks that are compatible with selected languages
            Frameworks.Clear();
            var compatibleFrameworks = new HashSet<string>();

            foreach (var language in selectedLanguages)
            {
                if (SelectedProjectType.LanguageFrameworks.ContainsKey(language))
                {
                    foreach (var framework in SelectedProjectType.LanguageFrameworks[language])
                    {
                        compatibleFrameworks.Add(framework);
                    }
                }
            }

            foreach (var frameworkName in compatibleFrameworks)
            {
                var framework = AllFrameworks.FirstOrDefault(f => f.Name == frameworkName);
                if (framework != null)
                {
                    var newFramework = new Framework
                    {
                        Name = framework.Name,
                        IsSelected = false
                    };
                    newFramework.PropertyChanged += (s, e) => {
                        if (e.PropertyName == nameof(Framework.IsSelected))
                        {
                            UpdateFilteredLibraries();
                        }
                    };
                    Frameworks.Add(newFramework);
                }
            }
        }



        private System.Windows.Media.Color? ShowColorDialog(System.Windows.Media.Color currentColor)
        {
            try
            {
                var colorDialog = new System.Windows.Forms.ColorDialog
                {
                    Color = System.Drawing.Color.FromArgb(currentColor.R, currentColor.G, currentColor.B),
                    FullOpen = true,
                    AllowFullOpen = true
                };

                if (colorDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    var selectedColor = colorDialog.Color;
                    return System.Windows.Media.Color.FromRgb(selectedColor.R, selectedColor.G, selectedColor.B);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"❌ Error opening color dialog: {ex.Message}";
            }

            return null;
        }

        // License management methods
        [RelayCommand]
        private void ShowActivationWindow()
        {
            var activationWindow = new ActivationWindow();
            var result = activationWindow.ShowDialog();
            
            if (result == true && activationWindow.ActivationSuccessful)
            {
                CheckLicenseStatus();
                OnPropertyChanged(nameof(CanUseAIEnhancement));
                OnPropertyChanged(nameof(CanUseAdvancedTemplates));
                OnPropertyChanged(nameof(CanUseExportOptions));
            }
        }

        private void CheckLicenseStatus()
        {
            var license = _licenseManager?.GetCurrentLicense();
            if (license != null && _licenseManager.IsLicenseValid())
            {
                IsLicensed = true;
                LicenseStatus = $"Licensed ({license.Type})";
                
                if (license.IsLifetime)
                {
                    LicenseInfo = "Lifetime License";
                }
                else
                {
                    var daysRemaining = license.DaysRemaining;
                    LicenseInfo = $"{daysRemaining} days remaining";
                }
            }
            else
            {
                IsLicensed = false;
                LicenseStatus = "Not Activated";
                LicenseInfo = "Click to activate";
            }
        }

        public bool CheckFeatureAccess(string featureName)
        {
            if (_licenseManager?.IsFeatureEnabled(featureName) == true)
            {
                return true;
            }

            // Show feature locked message
            var message = featureName switch
            {
                "AIEnhancement" => "AI Enhancement is a premium feature.\nPlease activate your license to unlock this feature.",
                "AdvancedTemplates" => "Advanced Templates are available in the full version.\nPlease activate your license to access this feature.",
                "ExportOptions" => "Export Options are available in the full version.\nPlease activate your license to access this feature.",
                _ => "This feature requires a valid license.\nPlease activate your license to continue."
            };

            MessageBox.Show(message, "Premium Feature", MessageBoxButton.OK, MessageBoxImage.Information);
            return false;
        }
    }
}
