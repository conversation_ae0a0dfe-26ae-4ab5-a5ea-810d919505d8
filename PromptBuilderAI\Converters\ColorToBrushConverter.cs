using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace PromptBuilderAI.Converters
{
    /// <summary>
    /// Converts a Color to a SolidColorBrush for XAML binding
    /// </summary>
    public class ColorToBrushConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Color color)
            {
                return new SolidColorBrush(color);
            }

            // Default fallback color
            return new SolidColorBrush(Colors.Gray);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is SolidColorBrush brush)
            {
                return brush.Color;
            }

            return Colors.Gray;
        }
    }
}
