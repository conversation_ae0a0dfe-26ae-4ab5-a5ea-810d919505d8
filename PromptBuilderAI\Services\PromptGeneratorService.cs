using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using PromptBuilderAI.Models;

namespace PromptBuilderAI.Services
{
	public class PromptGeneratorService : IPromptGeneratorService
	{
		private readonly IColorPaletteService _colorPaletteService;

		public PromptGeneratorService()
		{
			_colorPaletteService = new ColorPaletteService();
		}
		public string GeneratePrompt(string projectIdea, ProjectType projectType, List<ProgrammingLanguage> selectedLanguages, List<Framework> selectedFrameworks, List<ProjectLanguage> selectedProjectLanguages, ThemeSupport? themeSupport, ColorPalette? colorPalette)
		{
			var prompt = new StringBuilder();

			// === SYSTEM ROLE & IDENTITY ===
			prompt.AppendLine("# AI Development Assistant");
			prompt.AppendLine();
			prompt.AppendLine("## Role Definition");
			prompt.AppendLine("You are an **Expert Software Architect** and **Creative Code Conductor** with:");
			prompt.AppendLine("- 15+ years of experience in modern development practices");
			prompt.AppendLine("- Deep expertise in software architecture and design patterns");
			prompt.AppendLine("- Mastery of clean code principles and best practices");
			prompt.AppendLine("- Strong focus on user experience and aesthetic excellence");
			prompt.AppendLine();
			prompt.AppendLine("## Core Responsibilities");
			prompt.AppendLine("1. **Vision Holder**: Maintain the overall project vision and ensure consistency");
			prompt.AppendLine("2. **Intent Communicator**: Translate requirements into clear, actionable code");
			prompt.AppendLine("3. **Quality Guardian**: Ensure code quality, security, and performance standards");
			prompt.AppendLine("4. **Context Provider**: Deliver comprehensive, production-ready solutions");
			prompt.AppendLine();

			// === PROJECT SPECIFICATION ===
			prompt.AppendLine("# Project Specification");
			prompt.AppendLine();
			prompt.AppendLine("## Project Overview");
			prompt.AppendLine($"**Project Type**: {GetProjectTypeDisplayName(projectType)}");
			prompt.AppendLine($"**Core Purpose**: {projectIdea}");
			prompt.AppendLine($"**Development Approach**: Modern, scalable, and user-centric");
			prompt.AppendLine();

			// === TECHNICAL STACK ===
			prompt.AppendLine("# Technical Stack & Requirements");
			prompt.AppendLine();

			if (selectedLanguages.Any())
			{
				prompt.AppendLine("## Programming Languages");
				foreach (var lang in selectedLanguages)
				{
					prompt.AppendLine($"- **{lang.Name}**: Use modern syntax and best practices");
				}
				prompt.AppendLine();
			}

			if (selectedFrameworks.Any())
			{
				prompt.AppendLine("## Frameworks & Libraries");
				foreach (var framework in selectedFrameworks)
				{
					prompt.AppendLine($"- **{framework.Name}**: Implement according to official documentation and best practices");
				}
				prompt.AppendLine();
			}

			// === DEVELOPMENT GUIDELINES ===
			prompt.AppendLine("# Development Guidelines");
			prompt.AppendLine();

			prompt.AppendLine("## Core Principles");
			prompt.AppendLine("1. **Code Quality**: Write clean, maintainable, and well-documented code");
			prompt.AppendLine("2. **Best Practices**: Follow industry standards and modern development patterns");
			prompt.AppendLine("3. **Security**: Implement proper security measures and input validation");
			prompt.AppendLine("4. **Performance**: Optimize for performance and scalability");
			prompt.AppendLine("5. **User Experience**: Create intuitive and responsive interfaces");
			prompt.AppendLine();

			prompt.AppendLine("## Design Philosophy");
			prompt.AppendLine("- **Aesthetic Excellence**: Code should feel modern, clean, and purposeful");
			prompt.AppendLine("- **User-Centric Design**: Create intuitive interfaces that users can navigate effortlessly");
			prompt.AppendLine("- **Professional Quality**: Maintain professional yet approachable, robust yet elegant code");
			prompt.AppendLine();

			// === VISUAL & UX SPECIFICATIONS ===
			if (themeSupport != null || colorPalette != null)
			{
				prompt.AppendLine("# Visual & UX Specifications");
				prompt.AppendLine();

				if (themeSupport != null)
				{
					prompt.AppendLine("## Theme Support");
					prompt.AppendLine($"- **Theme System**: {themeSupport.Value}");
					prompt.AppendLine("- **Requirements**: Ensure smooth transitions between themes");
					prompt.AppendLine("- **Accessibility**: Maintain proper contrast ratios in all themes");
					prompt.AppendLine();
				}

				if (colorPalette != null && colorPalette.Value != ColorPalette.None)
				{
					prompt.AppendLine("## Color Palette");
					prompt.AppendLine($"- **Palette Type**: {colorPalette.Value}");
					prompt.AppendLine("- **Requirements**: Maintain consistency and accessibility standards");
					prompt.AppendLine("- **Implementation**: Follow WCAG guidelines for color contrast");
					prompt.AppendLine();

					// Add detailed color information
					var colorSection = _colorPaletteService.GenerateColorPromptSection(colorPalette.Value, projectType);
					prompt.AppendLine(colorSection);
				}
			}

			// === LOCALIZATION ===
			if (selectedProjectLanguages.Any())
			{
				prompt.AppendLine("# Localization & Internationalization");
				prompt.AppendLine();
				prompt.AppendLine("## Supported Languages");
				foreach (var lang in selectedProjectLanguages)
				{
					prompt.AppendLine($"- **{lang.Name}** ({lang.Code}): Full localization support");
				}
				prompt.AppendLine();
				prompt.AppendLine("## Implementation Requirements");
				prompt.AppendLine("- Use proper internationalization frameworks");
				prompt.AppendLine("- Support RTL languages where applicable");
				prompt.AppendLine("- Implement dynamic language switching");
				prompt.AppendLine("- Handle date, time, and number formatting");
				prompt.AppendLine();
			}

			// === PROJECT-SPECIFIC REQUIREMENTS ===
			prompt.AppendLine("# Project-Specific Requirements");
			prompt.AppendLine();
			prompt.AppendLine(GetProjectSpecificRequirements(projectType));
			prompt.AppendLine();

			// === IMPLEMENTATION GUIDELINES ===
			prompt.AppendLine("# Implementation Guidelines");
			prompt.AppendLine();

			prompt.AppendLine("## Architecture & Structure");
			prompt.AppendLine("1. **Modular Architecture**: Organize code into logical, reusable modules");
			prompt.AppendLine("2. **Separation of Concerns**: Maintain clear boundaries between layers");
			prompt.AppendLine("3. **Design Patterns**: Implement appropriate patterns (MVC, Repository, Factory, Observer)");
			prompt.AppendLine("4. **Dependency Injection**: Use DI containers for loose coupling");
			prompt.AppendLine("5. **SOLID Principles**: Follow SOLID design principles throughout");
			prompt.AppendLine();

			prompt.AppendLine("## Code Quality & Standards");
			prompt.AppendLine("- **Documentation**: Comprehensive inline comments and API documentation");
			prompt.AppendLine("- **Error Handling**: Robust exception handling with proper logging");
			prompt.AppendLine("- **Input Validation**: Validate all user inputs and external data");
			prompt.AppendLine("- **Code Style**: Follow language-specific style guides and conventions");
			prompt.AppendLine("- **Performance**: Optimize for performance and memory efficiency");
			prompt.AppendLine();

			prompt.AppendLine("## Testing & Quality Assurance");
			prompt.AppendLine("- **Unit Testing**: Comprehensive unit test coverage (>80%)");
			prompt.AppendLine("- **Integration Testing**: Test component interactions");
			prompt.AppendLine("- **End-to-End Testing**: Validate complete user workflows");
			prompt.AppendLine("- **Code Review**: Implement peer review processes");
			prompt.AppendLine("- **Static Analysis**: Use linting and static analysis tools");
			prompt.AppendLine();

			prompt.AppendLine("## Security & Performance");
			prompt.AppendLine("- **Security**: Implement authentication, authorization, and data protection");
			prompt.AppendLine("- **Data Validation**: Sanitize and validate all inputs");
			prompt.AppendLine("- **Performance**: Optimize database queries and API calls");
			prompt.AppendLine("- **Scalability**: Design for horizontal and vertical scaling");
			prompt.AppendLine("- **Monitoring**: Implement logging and performance monitoring");
			prompt.AppendLine();

			// === DELIVERABLES & EXPECTATIONS ===
			prompt.AppendLine("# Deliverables & Quality Expectations");
			prompt.AppendLine();

			prompt.AppendLine("## Required Deliverables");
			prompt.AppendLine("Provide a complete, production-ready implementation including:");
			prompt.AppendLine();
			prompt.AppendLine("1. **Source Code**");
			prompt.AppendLine("   - Well-structured, modular codebase");
			prompt.AppendLine("   - Comprehensive inline documentation");
			prompt.AppendLine("   - Clear naming conventions");
			prompt.AppendLine();
			prompt.AppendLine("2. **Setup & Configuration**");
			prompt.AppendLine("   - Step-by-step installation guide");
			prompt.AppendLine("   - Environment configuration instructions");
			prompt.AppendLine("   - Dependency management setup");
			prompt.AppendLine();
			prompt.AppendLine("3. **Documentation**");
			prompt.AppendLine("   - User guide with practical examples");
			prompt.AppendLine("   - API documentation (if applicable)");
			prompt.AppendLine("   - Architecture overview");
			prompt.AppendLine();
			prompt.AppendLine("4. **Deployment**");
			prompt.AppendLine("   - Production deployment guide");
			prompt.AppendLine("   - Environment-specific configurations");
			prompt.AppendLine("   - Performance optimization tips");
			prompt.AppendLine();

			prompt.AppendLine("## Quality Standards");
			prompt.AppendLine();
			prompt.AppendLine("### Code Excellence");
			prompt.AppendLine("- **Readability**: Code should tell a story - clear, self-documenting");
			prompt.AppendLine("- **Maintainability**: Easy to modify and extend");
			prompt.AppendLine("- **Reliability**: Robust error handling and edge case management");
			prompt.AppendLine("- **Accessibility**: Built-in accessibility features, not afterthoughts");
			prompt.AppendLine();

			prompt.AppendLine("### User Experience");
			prompt.AppendLine("- **Intuitive Design**: Users should understand functionality immediately");
			prompt.AppendLine("- **Responsive Performance**: Optimize for speed and responsiveness");
			prompt.AppendLine("- **Emotional Impact**: Consider the user's emotional journey");
			prompt.AppendLine("- **Error Recovery**: Graceful error handling that guides users");
			prompt.AppendLine();

			prompt.AppendLine("### Production Readiness");
			prompt.AppendLine("- **Security**: Implement security best practices from day one");
			prompt.AppendLine("- **Scalability**: Design for growth and increased load");
			prompt.AppendLine("- **Monitoring**: Include logging and performance tracking");
			prompt.AppendLine("- **Deployment**: Ready for production deployment");
			prompt.AppendLine();

			// Iterative Refinement Guidance
			prompt.AppendLine("## Implementation Approach");
			prompt.AppendLine("**Development Mindset:**");
			prompt.AppendLine("- Start with a solid foundation that can be iteratively enhanced");
			prompt.AppendLine("- Build components that are modular and easily testable");
			prompt.AppendLine("- Consider future extensibility without over-engineering");
			prompt.AppendLine("- Document decisions and trade-offs for future developers");
			prompt.AppendLine();

			prompt.AppendLine("**Final Notes:**");
			prompt.AppendLine("- Prioritize code quality and maintainability over quick solutions");
			prompt.AppendLine("- Follow the specified technology stack requirements");
			prompt.AppendLine("- Ensure the solution is scalable and production-ready");
			prompt.AppendLine("- Remember: Great code is not just functional—it's expressive and maintainable");

			return prompt.ToString();
		}

		/// <summary>
		/// Generates a structured prompt with enhanced organization and priority-based sections
		/// </summary>
		public string GenerateStructuredPrompt(string projectIdea, ProjectType projectType, StructuredPromptConfig config, List<ProgrammingLanguage> selectedLanguages, List<Framework> selectedFrameworks, List<ProjectLanguage> selectedProjectLanguages, ThemeSupport? themeSupport, ColorPalette? colorPalette)
		{
			var prompt = new StringBuilder();

			// Header with AI Identity
			prompt.AppendLine("# 🤖 AI Development Assistant");
			prompt.AppendLine();
			prompt.AppendLine("You are an expert software architect and creative code conductor with extensive experience in modern development practices.");
			prompt.AppendLine("Your role is to be a Vision Holder, Intent Communicator, Quality Guardian, and Context Provider.");
			prompt.AppendLine("Create a comprehensive, production-ready implementation that balances functionality with aesthetic excellence.");
			prompt.AppendLine();

			// Project Overview Section
			prompt.AppendLine("## 📋 Project Overview");
			prompt.AppendLine();
			prompt.AppendLine($"**Project Type:** {GetProjectTypeDisplayName(projectType)}");
			prompt.AppendLine($"**Core Idea:** {projectIdea}");
			prompt.AppendLine($"**Structure Type:** {config.StructureType}");
			prompt.AppendLine();

			// Technical Stack Section
			if (selectedLanguages.Any() || selectedFrameworks.Any())
			{
				prompt.AppendLine("## 🛠️ Technical Stack");
				prompt.AppendLine();

				if (selectedLanguages.Any())
				{
					prompt.AppendLine("**Programming Languages:**");
					foreach (var lang in selectedLanguages)
					{
						prompt.AppendLine($"- {lang.Name}");
					}
					prompt.AppendLine();
				}

				if (selectedFrameworks.Any())
				{
					prompt.AppendLine("**Frameworks & Libraries:**");
					foreach (var framework in selectedFrameworks)
					{
						prompt.AppendLine($"- {framework.Name}");
					}
					prompt.AppendLine();
				}
			}

			// Requirements Section (Priority-based)
			if (config.Requirements.Any())
			{
				prompt.AppendLine("## 📌 Requirements (Priority-Based)");
				prompt.AppendLine();

				var groupedRequirements = config.Requirements
					.GroupBy(r => r.Priority)
					.OrderBy(g => (int)g.Key);

				foreach (var group in groupedRequirements)
				{
					string priorityIcon = GetPriorityIcon(group.Key);
					prompt.AppendLine($"### {priorityIcon} {group.Key} Priority");
					prompt.AppendLine();

					foreach (var req in group)
					{
						string mandatoryText = req.IsMandatory ? " **(MANDATORY)**" : "";
						prompt.AppendLine($"**{req.Title}**{mandatoryText}");
						prompt.AppendLine($"{req.Description}");

						if (req.AcceptanceCriteria.Any())
						{
							prompt.AppendLine("*Acceptance Criteria:*");
							foreach (var criteria in req.AcceptanceCriteria)
							{
								prompt.AppendLine($"  - {criteria}");
							}
						}
						prompt.AppendLine();
					}
				}
			}

			// Constraints Section
			if (config.IncludeConstraints && config.Constraints.Any())
			{
				prompt.AppendLine("## ⚠️ Project Constraints");
				prompt.AppendLine();

				var groupedConstraints = config.Constraints
					.GroupBy(c => c.Type)
					.OrderBy(g => g.Key.ToString());

				foreach (var group in groupedConstraints)
				{
					string constraintIcon = GetConstraintIcon(group.Key);
					prompt.AppendLine($"### {constraintIcon} {group.Key} Constraints");
					prompt.AppendLine();

					foreach (var constraint in group)
					{
						prompt.AppendLine($"**{constraint.Title}**");
						prompt.AppendLine($"{constraint.Description}");
						if (!string.IsNullOrEmpty(constraint.Justification))
						{
							prompt.AppendLine($"*Justification: {constraint.Justification}*");
						}
						prompt.AppendLine();
					}
				}
			}

			// Success Criteria Section
			if (config.IncludeSuccessCriteria && config.SuccessCriteria.Any())
			{
				prompt.AppendLine("## 🎯 Success Criteria");
				prompt.AppendLine();

				var sortedCriteria = config.SuccessCriteria
					.OrderBy(sc => (int)sc.Priority);

				foreach (var criteria in sortedCriteria)
				{
					string priorityIcon = GetPriorityIcon(criteria.Priority);
					prompt.AppendLine($"**{priorityIcon} {criteria.Title}**");
					prompt.AppendLine($"{criteria.Description}");
					if (!string.IsNullOrEmpty(criteria.MeasurementMethod))
					{
						prompt.AppendLine($"*Measurement: {criteria.MeasurementMethod}*");
					}
					if (!string.IsNullOrEmpty(criteria.TargetValue))
					{
						prompt.AppendLine($"*Target: {criteria.TargetValue}*");
					}
					prompt.AppendLine();
				}
			}

			// Custom Sections
			if (config.Sections.Any())
			{
				var sortedSections = config.Sections
					.Where(s => s.IsRequired || config.StructureType != PromptStructureType.Minimal)
					.OrderBy(s => s.Order)
					.ThenBy(s => (int)s.Priority);

				foreach (var section in sortedSections)
				{
					string priorityIcon = GetPriorityIcon(section.Priority);
					prompt.AppendLine($"## {priorityIcon} {section.Title}");
					prompt.AppendLine();
					prompt.AppendLine(section.Content);
					prompt.AppendLine();

					if (section.SubSections.Any())
					{
						foreach (var subSection in section.SubSections)
						{
							prompt.AppendLine($"### {subSection}");
							prompt.AppendLine();
						}
					}
				}
			}

			// Project-specific requirements
			prompt.AppendLine("## 🔧 Project-Specific Requirements");
			prompt.AppendLine(GetProjectSpecificRequirements(projectType));
			prompt.AppendLine();

			// Implementation Guidelines
			prompt.AppendLine("## 📝 Implementation Guidelines");
			prompt.AppendLine();
			prompt.AppendLine("### Code Quality Standards");
			prompt.AppendLine("- **Documentation**: Include comprehensive comments and documentation");
			prompt.AppendLine("- **Error Handling**: Implement robust error handling and logging");
			prompt.AppendLine("- **Testing**: Include unit tests and integration tests where appropriate");
			prompt.AppendLine("- **Security**: Follow security best practices and input validation");
			prompt.AppendLine("- **Performance**: Optimize for performance and memory usage");
			prompt.AppendLine();

			// Custom Instructions
			if (!string.IsNullOrEmpty(config.CustomInstructions))
			{
				prompt.AppendLine("## 💡 Additional Instructions");
				prompt.AppendLine();
				prompt.AppendLine(config.CustomInstructions);
				prompt.AppendLine();
			}

			// Final Notes
			prompt.AppendLine("## 🎯 Final Notes");
			prompt.AppendLine();
			prompt.AppendLine("- Prioritize code quality and maintainability over quick solutions");
			prompt.AppendLine("- Follow the specified technology stack requirements");
			prompt.AppendLine("- Ensure the solution is scalable and production-ready");
			prompt.AppendLine("- Remember: Great code is not just functional—it's expressive and maintainable");

			// Apply length constraints if specified
			string result = prompt.ToString();
			if (config.MaxLength > 0 && result.Length > config.MaxLength)
			{
				// Truncate while preserving structure
				result = TruncatePromptIntelligently(result, config.MaxLength);
			}

			return result;
		}
		private string GetProjectTypeDisplayName(ProjectType projectType)
		{
			if (!true)
			{
			}
			string text;
			switch (projectType)
			{
			case ProjectType.MobileApp:
				text = "Mobile Application";
				break;
			case ProjectType.DesktopApp:
				text = "Desktop Application";
				break;
			case ProjectType.WebApp:
				text = "Web Application";
				break;
			case ProjectType.CmdScript:
				text = "Command Line Script";
				break;
			case ProjectType.BatScript:
				text = "Batch Script";
				break;
			default:
				text = projectType.ToString();
				break;
			}
			if (!true)
			{
			}
			return text;
		}
		private string GetProjectSpecificRequirements(ProjectType projectType)
		{
			var requirements = new StringBuilder();

			switch (projectType)
			{
				case ProjectType.MobileApp:
					requirements.AppendLine("### Mobile Application Requirements");
					requirements.AppendLine("**User Experience:** Create a mobile experience that feels native and intuitive, with smooth animations that delight users.");
					requirements.AppendLine();
					requirements.AppendLine("**Design Considerations:**");
					requirements.AppendLine("- Responsive design that adapts gracefully to different screen sizes and orientations");
					requirements.AppendLine("- Touch-friendly interface with intuitive gesture support and haptic feedback");
					requirements.AppendLine("- Native platform design guidelines and conventions");
					requirements.AppendLine();
					requirements.AppendLine("**Performance & User Experience:**");
					requirements.AppendLine("- Seamless offline functionality with intelligent data synchronization");
					requirements.AppendLine("- Battery-conscious design with efficient memory management");
					requirements.AppendLine("- Contextual push notifications that add value without being intrusive");
					requirements.AppendLine("- App store compliance with focus on user privacy and security");
					break;

				case ProjectType.DesktopApp:
					requirements.AppendLine("### Desktop Application Requirements");
					requirements.AppendLine("**Platform Integration:**");
					requirements.AppendLine("- Native look and feel following platform design guidelines");
					requirements.AppendLine("- Keyboard shortcuts and accessibility features");
					requirements.AppendLine("- Proper window management and state persistence");
					requirements.AppendLine();
					requirements.AppendLine("**System Integration:**");
					requirements.AppendLine("- File system integration and file associations");
					requirements.AppendLine("- System tray/menu bar integration");
					requirements.AppendLine("- Auto-updater functionality");
					requirements.AppendLine("- Installation package creation and system requirements");
					break;

				case ProjectType.WebApp:
					requirements.AppendLine("### Web Application Requirements");
					requirements.AppendLine("**Vibe Direction:** Build a web experience that feels fast, modern, and accessible to all users across devices.");
					requirements.AppendLine();
					requirements.AppendLine("**Frontend Excellence:**");
					requirements.AppendLine("- Universal browser compatibility with graceful degradation");
					requirements.AppendLine("- Fluid responsive design that works beautifully on any screen size");
					requirements.AppendLine("- Progressive Web App (PWA) features for app-like experience");
					requirements.AppendLine("- SEO optimization with semantic HTML and proper meta tags");
					requirements.AppendLine("- Smooth animations and micro-interactions that enhance usability");
					requirements.AppendLine();
					requirements.AppendLine("**Backend Architecture:**");
					requirements.AppendLine("- RESTful API design with intuitive endpoints and clear documentation");
					requirements.AppendLine("- Robust database schema with efficient queries and proper indexing");
					requirements.AppendLine("- Secure authentication and role-based authorization");
					requirements.AppendLine("- Comprehensive data validation with user-friendly error messages");
					requirements.AppendLine("- Security-first approach with rate limiting and proper headers");
					break;

				case ProjectType.CmdScript:
					requirements.AppendLine("### Command Line Script Requirements");
					requirements.AppendLine("**Interface Design:**");
					requirements.AppendLine("- Intuitive command-line argument parsing with help documentation");
					requirements.AppendLine("- Clear usage examples and error messages");
					requirements.AppendLine("- Progress indicators for long-running operations");
					requirements.AppendLine();
					requirements.AppendLine("**Functionality:**");
					requirements.AppendLine("- Robust error handling with appropriate exit codes");
					requirements.AppendLine("- Input validation and sanitization");
					requirements.AppendLine("- Configurable logging and debugging options");
					requirements.AppendLine("- Efficient execution with minimal resource usage");
					break;

				case ProjectType.BatScript:
					requirements.AppendLine("### Batch Script Requirements");
					requirements.AppendLine("**Windows Integration:**");
					requirements.AppendLine("- Windows batch file best practices and conventions");
					requirements.AppendLine("- Parameter validation and error handling");
					requirements.AppendLine("- Administrative privileges handling when required");
					requirements.AppendLine();
					requirements.AppendLine("**System Operations:**");
					requirements.AppendLine("- Safe registry and system file interactions");
					requirements.AppendLine("- Compatibility with different Windows versions");
					requirements.AppendLine("- Comprehensive logging and rollback capabilities");
					break;

				default:
					requirements.AppendLine("### General Requirements");
					requirements.AppendLine("- Follow industry best practices for the selected project type");
					requirements.AppendLine("- Implement comprehensive error handling and logging");
					requirements.AppendLine("- Ensure code maintainability and documentation");
					break;
			}

			return requirements.ToString();
		}

		/// <summary>
		/// Gets default structured prompt configuration based on structure type
		/// </summary>
		public StructuredPromptConfig GetDefaultStructureConfig(PromptStructureType structureType)
		{
			var config = new StructuredPromptConfig
			{
				StructureType = structureType
			};

			switch (structureType)
			{
				case PromptStructureType.Minimal:
					config.IncludeConstraints = false;
					config.IncludeSuccessCriteria = false;
					config.IncludeExamples = false;
					break;

				case PromptStructureType.Standard:
					config.IncludeConstraints = true;
					config.IncludeSuccessCriteria = true;
					config.IncludeExamples = true;
					break;

				case PromptStructureType.Detailed:
				case PromptStructureType.Enterprise:
				case PromptStructureType.Expert:
					config.IncludeConstraints = true;
					config.IncludeSuccessCriteria = true;
					config.IncludeExamples = true;
					break;

				case PromptStructureType.Beginner:
					config.IncludeConstraints = true;
					config.IncludeSuccessCriteria = true;
					config.IncludeExamples = true;
					config.CustomInstructions = "Please provide detailed explanations and step-by-step guidance suitable for beginners.";
					break;
			}

			return config;
		}

		/// <summary>
		/// Gets default sections for a project type and structure
		/// </summary>
		public List<PromptSection> GetDefaultSections(ProjectType projectType, PromptStructureType structureType)
		{
			var sections = new List<PromptSection>();

			// Common sections for all project types
			sections.Add(new PromptSection
			{
				Title = "Architecture & Design",
				Content = "Design a clean, maintainable architecture following SOLID principles and appropriate design patterns.",
				Priority = PriorityLevel.High,
				Order = 1,
				Icon = "Architecture"
			});

			sections.Add(new PromptSection
			{
				Title = "User Experience",
				Content = "Create an intuitive, accessible, and responsive user interface that provides excellent user experience.",
				Priority = PriorityLevel.High,
				Order = 2,
				Icon = "AccountCircle"
			});

			// Project-specific sections
			switch (projectType)
			{
				case ProjectType.WebApp:
					sections.Add(new PromptSection
					{
						Title = "SEO & Performance",
						Content = "Implement SEO best practices and optimize for performance with fast loading times.",
						Priority = PriorityLevel.Medium,
						Order = 3,
						Icon = "Speedometer"
					});
					break;

				case ProjectType.MobileApp:
					sections.Add(new PromptSection
					{
						Title = "Platform Optimization",
						Content = "Optimize for both iOS and Android platforms with native look and feel.",
						Priority = PriorityLevel.High,
						Order = 3,
						Icon = "Cellphone"
					});
					break;

				case ProjectType.DesktopApp:
					sections.Add(new PromptSection
					{
						Title = "System Integration",
						Content = "Integrate seamlessly with the operating system and provide native desktop experience.",
						Priority = PriorityLevel.Medium,
						Order = 3,
						Icon = "Desktop"
					});
					break;
			}

			// Add detailed sections for complex structure types
			if (structureType == PromptStructureType.Detailed || structureType == PromptStructureType.Enterprise)
			{
				sections.Add(new PromptSection
				{
					Title = "Security Implementation",
					Content = "Implement comprehensive security measures including input validation, authentication, and data protection.",
					Priority = PriorityLevel.Critical,
					Order = 4,
					Icon = "Security"
				});

				sections.Add(new PromptSection
				{
					Title = "Testing Strategy",
					Content = "Develop comprehensive testing strategy including unit tests, integration tests, and end-to-end tests.",
					Priority = PriorityLevel.High,
					Order = 5,
					Icon = "TestTube"
				});
			}

			return sections;
		}

		/// <summary>
		/// Gets default requirements for a project type
		/// </summary>
		public List<ProjectRequirement> GetDefaultRequirements(ProjectType projectType)
		{
			var requirements = new List<ProjectRequirement>();

			// Common requirements
			requirements.Add(new ProjectRequirement
			{
				Title = "Code Quality",
				Description = "Write clean, maintainable, and well-documented code following best practices.",
				Priority = PriorityLevel.Critical,
				IsMandatory = true,
				Category = "Development",
				AcceptanceCriteria = new List<string>
				{
					"Code follows established coding standards",
					"All functions and classes are properly documented",
					"Code is modular and reusable"
				}
			});

			requirements.Add(new ProjectRequirement
			{
				Title = "Error Handling",
				Description = "Implement comprehensive error handling and logging throughout the application.",
				Priority = PriorityLevel.High,
				IsMandatory = true,
				Category = "Reliability",
				AcceptanceCriteria = new List<string>
				{
					"All potential errors are caught and handled gracefully",
					"User-friendly error messages are displayed",
					"Errors are logged for debugging purposes"
				}
			});

			// Project-specific requirements
			switch (projectType)
			{
				case ProjectType.WebApp:
					requirements.Add(new ProjectRequirement
					{
						Title = "Responsive Design",
						Description = "Application must work seamlessly across all device sizes and orientations.",
						Priority = PriorityLevel.Critical,
						IsMandatory = true,
						Category = "UI/UX",
						AcceptanceCriteria = new List<string>
						{
							"Works on mobile devices (320px+)",
							"Works on tablets (768px+)",
							"Works on desktop (1024px+)"
						}
					});
					break;

				case ProjectType.MobileApp:
					requirements.Add(new ProjectRequirement
					{
						Title = "Mobile User Experience",
						Description = "Application must provide excellent mobile user experience.",
						Priority = PriorityLevel.Critical,
						IsMandatory = true,
						Category = "User Experience",
						AcceptanceCriteria = new List<string>
						{
							"Touch-friendly interface design",
							"Responsive layout for different screen sizes",
							"Native platform design guidelines"
						}
					});
					break;

				case ProjectType.DesktopApp:
					requirements.Add(new ProjectRequirement
					{
						Title = "Desktop Integration",
						Description = "Application should integrate well with the desktop environment.",
						Priority = PriorityLevel.High,
						IsMandatory = true,
						Category = "System Integration",
						AcceptanceCriteria = new List<string>
						{
							"Native look and feel",
							"Proper window management",
							"System tray integration"
						}
					});
					break;
			}

			return requirements;
		}

		/// <summary>
		/// Gets default constraints for a project type
		/// </summary>
		public List<ProjectConstraint> GetDefaultConstraints(ProjectType projectType)
		{
			var constraints = new List<ProjectConstraint>();

			// Common constraints
			constraints.Add(new ProjectConstraint
			{
				Title = "Performance Requirements",
				Description = "Application must load and respond quickly to user interactions.",
				Type = ConstraintType.Performance,
				Impact = PriorityLevel.High,
				Justification = "Poor performance leads to bad user experience and user abandonment."
			});

			constraints.Add(new ProjectConstraint
			{
				Title = "Security Standards",
				Description = "Must follow industry security standards and best practices.",
				Type = ConstraintType.Security,
				Impact = PriorityLevel.Critical,
				Justification = "Security vulnerabilities can lead to data breaches and legal issues."
			});

			// Project-specific constraints
			switch (projectType)
			{
				case ProjectType.WebApp:
					constraints.Add(new ProjectConstraint
					{
						Title = "Browser Compatibility",
						Description = "Must support modern browsers (Chrome, Firefox, Safari, Edge).",
						Type = ConstraintType.Technical,
						Impact = PriorityLevel.High,
						Justification = "Users access web applications through various browsers."
					});
					break;

				case ProjectType.MobileApp:
					constraints.Add(new ProjectConstraint
					{
						Title = "App Store Guidelines",
						Description = "Must comply with Apple App Store and Google Play Store guidelines.",
						Type = ConstraintType.Compliance,
						Impact = PriorityLevel.Critical,
						Justification = "Non-compliance prevents app publication and distribution."
					});
					break;
			}

			return constraints;
		}

		/// <summary>
		/// Gets default success criteria for a project type
		/// </summary>
		public List<SuccessCriteria> GetDefaultSuccessCriteria(ProjectType projectType)
		{
			var criteria = new List<SuccessCriteria>();

			// Common success criteria
			criteria.Add(new SuccessCriteria
			{
				Title = "Code Quality Score",
				Description = "Code should maintain high quality standards.",
				MeasurementMethod = "Static code analysis tools",
				TargetValue = "90%+ quality score",
				Priority = PriorityLevel.High
			});

			criteria.Add(new SuccessCriteria
			{
				Title = "Test Coverage",
				Description = "Adequate test coverage for reliability.",
				MeasurementMethod = "Code coverage tools",
				TargetValue = "80%+ code coverage",
				Priority = PriorityLevel.Medium
			});

			// Project-specific criteria
			switch (projectType)
			{
				case ProjectType.WebApp:
					criteria.Add(new SuccessCriteria
					{
						Title = "Page Load Speed",
						Description = "Fast loading times for better user experience.",
						MeasurementMethod = "Google PageSpeed Insights",
						TargetValue = "90+ performance score",
						Priority = PriorityLevel.High
					});
					break;

				case ProjectType.MobileApp:
					criteria.Add(new SuccessCriteria
					{
						Title = "App Startup Time",
						Description = "Quick app startup for better user experience.",
						MeasurementMethod = "App performance monitoring",
						TargetValue = "< 3 seconds cold start",
						Priority = PriorityLevel.High
					});
					break;
			}

			return criteria;
		}

		/// <summary>
		/// Gets priority icon for display
		/// </summary>
		private string GetPriorityIcon(PriorityLevel priority)
		{
			return priority switch
			{
				PriorityLevel.Critical => "🔴",
				PriorityLevel.High => "🟠",
				PriorityLevel.Medium => "🟡",
				PriorityLevel.Low => "🟢",
				PriorityLevel.Optional => "⚪",
				_ => "🔵"
			};
		}

		/// <summary>
		/// Gets constraint icon for display
		/// </summary>
		private string GetConstraintIcon(ConstraintType constraintType)
		{
			return constraintType switch
			{
				ConstraintType.Technical => "⚙️",
				ConstraintType.Business => "💼",
				ConstraintType.Time => "⏰",
				ConstraintType.Budget => "💰",
				ConstraintType.Security => "🔒",
				ConstraintType.Performance => "⚡",
				ConstraintType.Accessibility => "♿",
				ConstraintType.Compliance => "📋",
				_ => "⚠️"
			};
		}

		/// <summary>
		/// Intelligently truncates prompt while preserving structure
		/// </summary>
		private string TruncatePromptIntelligently(string prompt, int maxLength)
		{
			if (prompt.Length <= maxLength) return prompt;

			// Find the last complete section that fits within the limit
			var lines = prompt.Split('\n');
			var result = new StringBuilder();
			var currentLength = 0;

			foreach (var line in lines)
			{
				if (currentLength + line.Length + 1 > maxLength)
				{
					result.AppendLine();
					result.AppendLine("[Content truncated to meet length constraints]");
					break;
				}

				result.AppendLine(line);
				currentLength += line.Length + 1;
			}

			return result.ToString();
		}
	}
}
