﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <ProjectGuid>{E8F4A2A2-3ACD-43E0-87B1-A6B934EC96A4}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>PromptBuilderAI</RootNamespace>
    <AssemblyName>PromptBuilderAI</AssemblyName>
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
    <FileAlignment>512</FileAlignment>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>True</UseWPF>
    <UseWindowsForms>True</UseWindowsForms>
  </PropertyGroup>
  <PropertyGroup>
    <EnableDefaultItems>False</EnableDefaultItems>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>app.manifest</ApplicationManifest>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
    </Compile>

    <Compile Include="Converters\BooleanToVisibilityConverter.cs" />
    <Compile Include="Converters\ColorToBrushConverter.cs" />
    <Compile Include="Converters\InverseBooleanConverter.cs" />
    <Compile Include="Converters\InverseBooleanToVisibilityConverter.cs" />
    <Compile Include="MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
    </Compile>

    <Compile Include="Models\ColorPalette.cs" />
    <Compile Include="Models\Framework.cs" />
    <Compile Include="Models\ModelPricing.cs" />
    <Compile Include="Models\ModelsResponse.cs" />
    <Compile Include="Models\OpenRouterModel.cs" />
    <Compile Include="Models\ProgrammingLanguage.cs" />
    <Compile Include="Models\ProjectLanguage.cs" />
    <Compile Include="Models\ProjectType.cs" />
    <Compile Include="Models\ProjectTypeInfo.cs" />
    <Compile Include="Models\PromptHistory.cs" />
    <Compile Include="Models\ThemeSupport.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <DependentUpon>Settings.settings</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="Services\ColorPaletteService.cs" />
    <Compile Include="Services\IOpenRouterService.cs" />
    <Compile Include="Services\IPromptGeneratorService.cs" />
    <Compile Include="Services\IPromptHistoryService.cs" />
    <Compile Include="Services\ISecureStorageService.cs" />
    <Compile Include="Services\OpenRouterService.cs" />
    <Compile Include="Services\PromptGeneratorService.cs" />
    <Compile Include="Services\PromptHistoryService.cs" />
    <Compile Include="Services\RateLimitService.cs" />
    <Compile Include="Services\SecureStorageService.cs" />

    <Compile Include="ViewModels\MainViewModel.cs" />
    <Compile Include="ViewModels\SettingsViewModel.cs" />
    <Compile Include="Views\SettingsWindow.xaml.cs">
      <DependentUpon>SettingsWindow.xaml</DependentUpon>
    </Compile>

  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="LiteDB" Version="5.0.17" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="OpenAI" Version="1.11.0" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.manifest" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </ApplicationDefinition>
  </ItemGroup>
  <ItemGroup>
    <Page Include="MainWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\SettingsWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>

  </ItemGroup>
</Project>