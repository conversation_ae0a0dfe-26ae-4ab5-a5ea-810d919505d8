using System;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Security.Cryptography;
using System.Text;
using SerialGeneratorTool.Models;

namespace SerialGeneratorTool.Services
{
    public class SerialGenerator
    {
        private const string PRODUCT_CODE = "PB25";
        private const string DATABASE_FILE = "ActivationDatabase.json";
        private static readonly Random _random = new();

        private ActivationDatabase _database = new();

        public SerialGenerator()
        {
            LoadDatabase();
        }

        public string GenerateSerial(LicenseType type, int durationDays = 0)
        {
            string serial;

            // تأكد من عدم تكرار السيريال
            do
            {
                var typeCode = GetTypeCode(type, durationDays);
                var dataCode = GenerateDataCode();
                var checksum = GenerateChecksum(PRODUCT_CODE, typeCode, dataCode);
                serial = $"{PRODUCT_CODE}-{typeCode}-{dataCode}-{checksum}";
            }
            while (IsSerialExists(serial));

            // إضافة السيريال لقاعدة البيانات كغير مستخدم
            var record = new SerialUsageRecord
            {
                SerialNumber = serial,
                IsUsed = false,
                CreatedDate = DateTime.Now,
                Type = type,
                DurationDays = durationDays,
                ExpiryDate = type == LicenseType.Lifetime ? null : DateTime.Now.AddDays(durationDays)
            };

            _database.UsedSerials.Add(record);
            SaveDatabase();

            return serial;
        }        public ActivationResult ActivateSerial(string serial, string machineFingerprint)
        {
            var record = _database.UsedSerials.FirstOrDefault(x => x.SerialNumber == serial);

            if (record == null)
            {
                return new ActivationResult
                {
                    Success = false,
                    ErrorMessage = "Serial number not found or invalid."
                };
            }

            if (record.IsUsed)
            {
                return new ActivationResult
                {
                    Success = false,
                    ErrorMessage = "This serial number has already been used.",
                    UsedDate = record.ActivationDate,
                    UsedMachine = record.MachineFingerprint
                };
            }

            // تحقق من انتهاء الصلاحية
            if (record.ExpiryDate.HasValue && DateTime.Now > record.ExpiryDate.Value)
            {
                return new ActivationResult
                {
                    Success = false,
                    ErrorMessage = "This serial number has expired."
                };
            }

            // تفعيل السيريال
            record.IsUsed = true;
            record.ActivationDate = DateTime.Now;
            record.MachineFingerprint = machineFingerprint;
            record.ActivationId = Guid.NewGuid().ToString();

            SaveDatabase();

            return new ActivationResult
            {
                Success = true,
                LicenseInfo = CreateLicenseInfo(record),
                ActivationId = record.ActivationId
            };
        }        public bool IsSerialUsed(string serial)
        {
            var record = _database.UsedSerials.FirstOrDefault(x => x.SerialNumber == serial);
            return record?.IsUsed ?? false;
        }

        public SerialUsageRecord? GetSerialInfo(string serial)
        {
            return _database.UsedSerials.FirstOrDefault(x => x.SerialNumber == serial);
        }

        private bool IsSerialExists(string serial)
        {
            return _database.UsedSerials.Any(x => x.SerialNumber == serial);
        }

        private string GetTypeCode(LicenseType type, int days)
        {
            return type switch
            {
                LicenseType.Lifetime => "LT00",
                LicenseType.Trial when days <= 30 => $"T{days:D3}",
                LicenseType.TimeLimited => $"T{days:D3}",
                _ => "T030" // Default 30 days
            };
        }

        private string GenerateDataCode()
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var result = new char[4];

            for (int i = 0; i < 4; i++)
            {
                result[i] = chars[_random.Next(chars.Length)];
            }

            return new string(result);
        }

        private string GenerateChecksum(string productCode, string typeCode, string dataCode)
        {
            var combined = productCode + typeCode + dataCode;
            var hash = combined.GetHashCode();
            var checksum = Math.Abs(hash % 46656); // 36^3 for 3 chars

            return ConvertToBase36(checksum).PadLeft(4, '0');
        }        private string ConvertToBase36(int value)
        {
            const string chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            var result = "";

            do
            {
                result = chars[value % 36] + result;
                value /= 36;
            } while (value > 0);

            return result;
        }

        private LicenseInfo CreateLicenseInfo(SerialUsageRecord record)
        {
            return new LicenseInfo
            {
                SerialNumber = record.SerialNumber,
                Type = record.Type,
                Duration = new LicenseDuration
                {
                    Days = record.DurationDays,
                    ExpiryDate = record.ExpiryDate
                },
                CreatedDate = record.CreatedDate,
                ExpiryDate = record.ExpiryDate,
                IsValid = true
            };
        }

        private void LoadDatabase()
        {
            try
            {
                if (File.Exists(DATABASE_FILE))
                {
                    var json = File.ReadAllText(DATABASE_FILE);
                    _database = JsonSerializer.Deserialize<ActivationDatabase>(json) ?? new ActivationDatabase();
                }
                else
                {
                    _database = new ActivationDatabase();
                }
            }
            catch
            {
                _database = new ActivationDatabase();
            }
        }        private void SaveDatabase()
        {
            try
            {
                _database.LastUpdated = DateTime.Now;
                var options = new JsonSerializerOptions { WriteIndented = true };
                var json = JsonSerializer.Serialize(_database, options);
                File.WriteAllText(DATABASE_FILE, json);
            }
            catch (Exception ex)
            {
                // Log error
                Console.WriteLine($"Error saving database: {ex.Message}");
            }
        }

        public string GenerateMachineFingerprint()
        {
            var machineInfo = $"{Environment.MachineName}-{Environment.UserName}-{Environment.OSVersion}";
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(machineInfo));
            return Convert.ToBase64String(hash)[..16]; // أول 16 حرف
        }
    }
}