using System;
using System.CodeDom.Compiler;
using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Extensions.DependencyInjection;
using PromptBuilderAI.Services;
using PromptBuilderAI.ViewModels;
using MaterialDesignThemes.Wpf;

namespace PromptBuilderAI
{
	public partial class App : Application
	{
		private void Application_Startup(object sender, StartupEventArgs e)
		{
			try
			{
				// Initialize MaterialDesign with saved theme
				var paletteHelper = new PaletteHelper();
				var theme = paletteHelper.GetTheme();

				// Apply saved theme settings
				ApplySavedTheme(paletteHelper, theme);

				paletteHelper.SetTheme(theme);

				// Configure Dependency Injection
				var services = new ServiceCollection();
				ConfigureServices(services);
				_serviceProvider = services.BuildServiceProvider();

				// Create the main window with proper DI
				var mainWindow = new MainWindow
				{
					DataContext = _serviceProvider.GetRequiredService<MainViewModel>()
				};

				// Ensure window is visible and in foreground
				mainWindow.Visibility = Visibility.Visible;
				mainWindow.WindowState = WindowState.Normal;
				mainWindow.Show();
				mainWindow.Activate();
				mainWindow.Focus();
			}
			catch (Exception ex)
			{
				MessageBox.Show($"Error starting application: {ex.Message}\n\n{ex.StackTrace}", "Startup Error", MessageBoxButton.OK, MessageBoxImage.Error);
				Shutdown();
			}
		}
		private void ConfigureServices(ServiceCollection services)
		{
			services.AddHttpClient<IOpenRouterService, OpenRouterService>();
			services.AddSingleton<IPromptGeneratorService, PromptGeneratorService>();
			services.AddSingleton<IPromptHistoryService, PromptHistoryService>();
			services.AddSingleton<ISecureStorageService, SecureStorageService>();
			services.AddTransient<MainViewModel>(provider => new MainViewModel(
				provider.GetRequiredService<IPromptGeneratorService>(),
				provider.GetRequiredService<IPromptHistoryService>(),
				provider.GetRequiredService<IOpenRouterService>(),
				provider.GetRequiredService<ISecureStorageService>()));
			services.AddTransient<SettingsViewModel>(provider => new SettingsViewModel(
				provider.GetRequiredService<ISecureStorageService>(),
				provider.GetRequiredService<IOpenRouterService>()));

		}
		private void ApplySavedTheme(PaletteHelper paletteHelper, ITheme theme)
		{
			try
			{
				// Load saved theme preference
				bool isDarkMode = PromptBuilderAI.Properties.Settings.Default.IsDarkMode;

				if (isDarkMode)
				{
					// Apply Dark Mode with custom #181818 color
					theme.SetBaseTheme(Theme.Dark);

					// Apply custom dark background colors
					theme.Paper = System.Windows.Media.Color.FromRgb(0x18, 0x18, 0x18);
					theme.CardBackground = System.Windows.Media.Color.FromRgb(0x20, 0x20, 0x20);
					theme.ToolBarBackground = System.Windows.Media.Color.FromRgb(0x25, 0x25, 0x25);
				}
				else
				{
					// Apply Light Mode
					theme.SetBaseTheme(Theme.Light);
				}
			}
			catch (Exception ex)
			{
				// If there's an error, default to light mode
				theme.SetBaseTheme(Theme.Light);
				System.Diagnostics.Debug.WriteLine($"Error applying saved theme: {ex.Message}");
			}
		}

		protected override void OnExit(ExitEventArgs e)
		{
			ServiceProvider serviceProvider = this._serviceProvider;
			if (serviceProvider != null)
			{
				serviceProvider.Dispose();
			}
			base.OnExit(e);
		}
		public ServiceProvider _serviceProvider;
	}
}
