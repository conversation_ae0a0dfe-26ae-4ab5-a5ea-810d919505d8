<Window
	xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
	xmlns:converters="clr-namespace:PromptBuilderAI.Converters"
	xmlns:promptbuilderai="clr-namespace:PromptBuilderAI" xmlns:av="http://schemas.microsoft.com/expression/blend/2008" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="av"
	x:Class="PromptBuilderAI.MainWindow"
	Title="Prompt Builder AI"
	Height="900"
	Width="1200"
	MinHeight="700"
	MinWidth="900"
	WindowStartupLocation="CenterScreen"
	TextElement.Foreground="{DynamicResource MaterialDesignBody}"
	TextElement.FontWeight="Regular"
	TextElement.FontSize="13"
	TextOptions.TextFormattingMode="Ideal"
	TextOptions.TextRenderingMode="Auto"
	Background="{DynamicResource MaterialDesignPaper}"
	FontFamily="{DynamicResource MaterialDesignFont}"
	>
    <materialDesign:DialogHost
		Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition
					Height="Auto" />
                <RowDefinition
					Height="*" />
                <RowDefinition
					Height="Auto" />
            </Grid.RowDefinitions>
            <materialDesign:ColorZone
				Grid.Row="0"
				Mode="PrimaryMid"
				Padding="16"
				Background="{DynamicResource MaterialDesignToolBarBackground}"
				Foreground="{DynamicResource MaterialDesignBody}">
                <DockPanel>
                    <materialDesign:PackIcon
						Kind="Robot"
						Width="32"
						Height="32"
						VerticalAlignment="Center"
						DockPanel.Dock="Left"
						Foreground="{DynamicResource PrimaryHueMidBrush}" />
                    <TextBlock
						Text="Prompt Builder AI"
						Style="{StaticResource MaterialDesignHeadline5TextBlock}"
						VerticalAlignment="Center"
						Margin="16,0,0,0"
						Foreground="{DynamicResource MaterialDesignBody}" />
                    <Button
						DockPanel.Dock="Right"
						Style="{StaticResource MaterialDesignIconButton}"
						ToolTip="Settings"
						Foreground="{DynamicResource MaterialDesignBody}"
						Margin="8,0,0,0"
						Command="{Binding OpenSettingsCommand}">
                        <materialDesign:PackIcon
							Kind="Settings"
							Width="24"
							Height="24" />
                    </Button>
                    <Button
						DockPanel.Dock="Right"
						Style="{StaticResource MaterialDesignIconButton}"
						ToolTip="Toggle Dark/Light Mode"
						Foreground="{DynamicResource MaterialDesignBody}"
						Margin="8,0,0,0"
						Command="{Binding ToggleThemeCommand}">
                        <materialDesign:PackIcon
							Kind="Brightness6"
							Width="24"
							Height="24" />
                    </Button>
                    <TextBlock
						Text="Convert your ideas into professional LLM prompts"
						Style="{StaticResource MaterialDesignBody2TextBlock}"
						VerticalAlignment="Center"
						Margin="16,0,16,0"
						Foreground="{DynamicResource MaterialDesignBodyLight}"
						Opacity="0.8"
						HorizontalAlignment="Right" />
                </DockPanel>
            </materialDesign:ColorZone>
            <ScrollViewer
				Grid.Row="1"
				VerticalScrollBarVisibility="Auto">
                <Grid
					Margin="16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition
							Width="2*" />
                        <ColumnDefinition
							Width="*" />
                    </Grid.ColumnDefinitions>
                    <StackPanel
						Grid.Column="0"
						Margin="0,0,8,0">
                        <materialDesign:Card
							Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock
									Text="Project Idea"
									Style="{StaticResource SectionHeaderStyle}" />
                                <TextBox
									materialDesign:HintAssist.Hint="Describe your project idea in plain language..."
									Style="{StaticResource MaterialDesignOutlinedTextBox}"
									AcceptsReturn="True"
									TextWrapping="Wrap"
									MinHeight="80"
									MaxHeight="120"
									VerticalScrollBarVisibility="Auto"
									Text="{Binding ProjectIdea, UpdateSourceTrigger=PropertyChanged}" />
                            </StackPanel>
                        </materialDesign:Card>
                        <materialDesign:Card
							Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock
									Text="Project Type"
									Style="{StaticResource SectionHeaderStyle}" />
                                <ComboBox
									materialDesign:HintAssist.Hint="Select project type"
									Style="{StaticResource MaterialDesignOutlinedComboBox}"
									Margin="0,8"
									ItemsSource="{Binding ProjectTypeValues}"
									SelectedItem="{Binding SelectedProjectType}"
									DisplayMemberPath="DisplayName" />
                            </StackPanel>
                        </materialDesign:Card>
                        <materialDesign:Card
							Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock
									Text="Programming Languages"
									Style="{StaticResource SectionHeaderStyle}" />
                                <TextBlock
									Text="Select the programming languages for your project:"
									Style="{StaticResource SubHeaderStyle}" />
                                <ItemsControl
									ItemsSource="{Binding ProgrammingLanguages}">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <WrapPanel
												Orientation="Horizontal" />
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <CheckBox
												Margin="0,4,16,4"
												Style="{StaticResource MaterialDesignCheckBox}"
												Content="{Binding Name}"
												IsChecked="{Binding IsSelected}" />
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </materialDesign:Card>
                        <materialDesign:Card
							Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock
									Text="Frameworks &amp; Libraries"
									Style="{StaticResource SectionHeaderStyle}" />
                                <TextBlock
									Text="Select the frameworks or libraries to use:"
									Style="{StaticResource SubHeaderStyle}" />
                                <ItemsControl
									ItemsSource="{Binding Frameworks}">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <WrapPanel
												Orientation="Horizontal" />
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <CheckBox
												Margin="0,4,16,4"
												Style="{StaticResource MaterialDesignCheckBox}"
												Content="{Binding Name}"
												IsChecked="{Binding IsSelected}" />
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </materialDesign:Card>
                        <materialDesign:Card
							Style="{StaticResource CardStyle}"
							Visibility="{Binding ShowThemeOptions, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel>
                                <TextBlock
									Text="UI Options"
									Style="{StaticResource SectionHeaderStyle}" />
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition
											Width="*" />
                                        <ColumnDefinition
											Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <StackPanel
										Grid.Column="0"
										Margin="0,0,8,0">
                                        <TextBlock
											Text="Theme Support:"
											Style="{StaticResource SubHeaderStyle}" />
                                        <ComboBox
											Style="{StaticResource MaterialDesignOutlinedComboBox}"
											ItemsSource="{Binding ThemeSupportValues}"
											SelectedItem="{Binding SelectedThemeSupport}" />
                                    </StackPanel>
                                    <StackPanel
										Grid.Column="1"
										Margin="8,0,0,0"
										Visibility="Collapsed">
                                        <TextBlock
											Text="Color Palette:"
											Style="{StaticResource SubHeaderStyle}" />
                                        <ComboBox
											Style="{StaticResource MaterialDesignOutlinedComboBox}"
											ItemsSource="{Binding ColorPaletteValues}"
											SelectedItem="{Binding SelectedColorPalette}" />
                                    </StackPanel>
                                </Grid>

                                <!-- Design Library Selection -->
                                <StackPanel Margin="0,16,0,0"
                                            Visibility="{Binding ShowDesignLibrarySelection, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <TextBlock Text="Design Library:" Style="{StaticResource SubHeaderStyle}" />
                                    <ComboBox Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                              ItemsSource="{Binding DesignLibraries}"
                                              SelectedItem="{Binding SelectedDesignLibrary}"
                                              materialDesign:HintAssist.Hint="Choose a design library (optional)"
                                              Margin="0,4,0,0">
                                        <ComboBox.ItemTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Vertical" Margin="0,4">
                                                    <TextBlock Text="{Binding Name}" FontWeight="SemiBold" />
                                                    <TextBlock Text="{Binding UserDescription}"
                                                               FontSize="11" Opacity="0.7"
                                                               TextWrapping="Wrap"
                                                               Visibility="{Binding UserDescription, Converter={StaticResource BooleanToVisibilityConverter}}" />
                                                    <TextBlock Text="{Binding Description}"
                                                               FontSize="11" Opacity="0.7"
                                                               TextWrapping="Wrap"
                                                               Visibility="{Binding UserDescription, Converter={StaticResource InverseBooleanToVisibilityConverter}}" />
                                                    <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                                                        <TextBlock Text="{Binding Language}"
                                                                   FontSize="10"
                                                                   Background="{DynamicResource MaterialDesignChipBackground}"
                                                                   Foreground="{DynamicResource MaterialDesignBody}"
                                                                   Padding="4,2" Margin="0,0,4,0"
                                                                   Visibility="{Binding Language, Converter={StaticResource BooleanToVisibilityConverter}}" />
                                                        <TextBlock Text="{Binding Framework}"
                                                                   FontSize="10"
                                                                   Background="{DynamicResource MaterialDesignSelection}"
                                                                   Foreground="{DynamicResource MaterialDesignBody}"
                                                                   Padding="4,2"
                                                                   Visibility="{Binding Framework, Converter={StaticResource BooleanToVisibilityConverter}}" />
                                                    </StackPanel>
                                                </StackPanel>
                                            </DataTemplate>
                                        </ComboBox.ItemTemplate>
                                    </ComboBox>
                                </StackPanel>

                                <!-- Simple Color Selection -->
                                <Expander Margin="0,16,0,0">
                                    <Expander.Header>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>

                                            <CheckBox Grid.Column="0"
                                                      IsChecked="{Binding IsColorSelectionEnabled, Mode=TwoWay}"
                                                      Style="{StaticResource MaterialDesignCheckBox}"
                                                      ToolTip="Enable or disable color selection for your project"
                                                      VerticalAlignment="Center"
                                                      Margin="0,0,8,0" />

                                            <TextBlock Grid.Column="1" Text="🎨 Color Selection"
                                                       VerticalAlignment="Center" FontWeight="SemiBold" />
                                        </Grid>
                                    </Expander.Header>
                                    <StackPanel Margin="0,8"
                                                Visibility="{Binding IsColorSelectionEnabled, Converter={StaticResource BooleanToVisibilityConverter}}">
                                        <TextBlock Text="Choose your project colors:" Style="{StaticResource SubHeaderStyle}" Margin="0,0,0,12" />

                                        <!-- Primary Color -->
                                        <Grid Margin="0,0,0,12">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="60" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>

                                            <materialDesign:PackIcon Grid.Column="0" Kind="Palette" VerticalAlignment="Center" Margin="0,0,8,0" />
                                            <Button Grid.Column="1" Height="30" Style="{StaticResource MaterialDesignFlatButton}"
                                                    Command="{Binding SelectPrimaryColorCommand}" Padding="0"
                                                    ToolTip="Click to change Primary Color" Cursor="Hand">
                                                <Border CornerRadius="4" BorderThickness="2" BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                        Width="50" Height="26">
                                                    <Border.Background>
                                                        <SolidColorBrush Color="{Binding PrimaryColor}" />
                                                    </Border.Background>
                                                    <Border.Style>
                                                        <Style TargetType="Border">
                                                            <Style.Triggers>
                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                    <Setter Property="BorderThickness" Value="3" />
                                                                    <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignSelection}" />
                                                                </Trigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Border.Style>
                                                </Border>
                                            </Button>
                                            <StackPanel Grid.Column="2" Margin="12,0,0,0" VerticalAlignment="Center">
                                                <TextBlock Text="Primary Color" FontWeight="SemiBold" />
                                                <TextBlock Text="Material Design deep purple - elegant and professional for main UI elements."
                                                           FontSize="11" Opacity="0.7" TextWrapping="Wrap" />
                                            </StackPanel>
                                        </Grid>

                                        <!-- Background Color -->
                                        <Grid Margin="0,0,0,12">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="60" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>

                                            <materialDesign:PackIcon Grid.Column="0" Kind="Home" VerticalAlignment="Center" Margin="0,0,8,0" />
                                            <Button Grid.Column="1" Height="30" Style="{StaticResource MaterialDesignFlatButton}"
                                                    Command="{Binding SelectBackgroundColorCommand}" Padding="0"
                                                    ToolTip="Click to change Background Color" Cursor="Hand">
                                                <Border CornerRadius="4" BorderThickness="2" BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                        Width="50" Height="26">
                                                    <Border.Background>
                                                        <SolidColorBrush Color="{Binding BackgroundColor}" />
                                                    </Border.Background>
                                                    <Border.Style>
                                                        <Style TargetType="Border">
                                                            <Style.Triggers>
                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                    <Setter Property="BorderThickness" Value="3" />
                                                                    <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignSelection}" />
                                                                </Trigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Border.Style>
                                                </Border>
                                            </Button>
                                            <StackPanel Grid.Column="2" Margin="12,0,0,0" VerticalAlignment="Center">
                                                <TextBlock Text="Background Color" FontWeight="SemiBold" />
                                                <TextBlock Text="Light purple background - soft and calming for content areas."
                                                           FontSize="11" Opacity="0.7" TextWrapping="Wrap" />
                                            </StackPanel>
                                        </Grid>

                                        <!-- Accent Color -->
                                        <Grid Margin="0,0,0,12">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="60" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>

                                            <materialDesign:PackIcon Grid.Column="0" Kind="Star" VerticalAlignment="Center" Margin="0,0,8,0" />
                                            <Button Grid.Column="1" Height="30" Style="{StaticResource MaterialDesignFlatButton}"
                                                    Command="{Binding SelectAccentColorCommand}" Padding="0"
                                                    ToolTip="Click to change Accent Color" Cursor="Hand">
                                                <Border CornerRadius="4" BorderThickness="2" BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                        Width="50" Height="26">
                                                    <Border.Background>
                                                        <SolidColorBrush Color="{Binding AccentColor}" />
                                                    </Border.Background>
                                                    <Border.Style>
                                                        <Style TargetType="Border">
                                                            <Style.Triggers>
                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                    <Setter Property="BorderThickness" Value="3" />
                                                                    <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignSelection}" />
                                                                </Trigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Border.Style>
                                                </Border>
                                            </Button>
                                            <StackPanel Grid.Column="2" Margin="12,0,0,0" VerticalAlignment="Center">
                                                <TextBlock Text="Accent Color" FontWeight="SemiBold" />
                                                <TextBlock Text="Material orange - vibrant and energetic for buttons and highlights."
                                                           FontSize="11" Opacity="0.7" TextWrapping="Wrap" />
                                            </StackPanel>
                                        </Grid>

                                        <!-- Color Values Display -->
                                        <Border Background="{DynamicResource MaterialDesignCardBackground}"
                                                CornerRadius="4" Padding="12" Margin="0,8,0,0">
                                            <StackPanel>
                                                <TextBlock Text="🎨 Selected Colors" FontWeight="SemiBold" Margin="0,0,0,8" />
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*" />
                                                        <ColumnDefinition Width="*" />
                                                        <ColumnDefinition Width="*" />
                                                    </Grid.ColumnDefinitions>

                                                    <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                                        <TextBlock Text="Primary" FontSize="10" Opacity="0.7" />
                                                        <TextBlock Text="{Binding PrimaryColorHex}" FontFamily="Consolas" FontWeight="Bold" />
                                                    </StackPanel>

                                                    <StackPanel Grid.Column="1" Margin="0,0,8,0">
                                                        <TextBlock Text="Background" FontSize="10" Opacity="0.7" />
                                                        <TextBlock Text="{Binding BackgroundColorHex}" FontFamily="Consolas" FontWeight="Bold" />
                                                    </StackPanel>

                                                    <StackPanel Grid.Column="2">
                                                        <TextBlock Text="Accent" FontSize="10" Opacity="0.7" />
                                                        <TextBlock Text="{Binding AccentColorHex}" FontFamily="Consolas" FontWeight="Bold" />
                                                    </StackPanel>
                                                </Grid>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>
                                </Expander>
                            </StackPanel>
                        </materialDesign:Card>
                        <materialDesign:Card
							Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock
									Text="Project Languages"
									Style="{StaticResource SectionHeaderStyle}" />
                                <TextBlock
									Text="Select the languages your project should support:"
									Style="{StaticResource SubHeaderStyle}" />
                                <ItemsControl
									ItemsSource="{Binding ProjectLanguages}">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <WrapPanel
												Orientation="Horizontal" />
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <CheckBox
												Margin="0,4,16,4"
												Style="{StaticResource MaterialDesignCheckBox}"
												IsChecked="{Binding IsSelected}">
                                                <StackPanel
													Orientation="Horizontal">
                                                    <TextBlock
														Text="{Binding Name}" />
                                                    <TextBlock
														Opacity="0.6"
														Text="{Binding Code, StringFormat=' (\{0\})'}" />
                                                </StackPanel>
                                            </CheckBox>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </materialDesign:Card>



                        <materialDesign:Card
							Style="{StaticResource CardStyle}">
                            <StackPanel
								Orientation="Horizontal"
								HorizontalAlignment="Center">
                                <Button
									Style="{StaticResource PrimaryButtonStyle}"
									Command="{Binding GeneratePromptCommand}"
									IsEnabled="{Binding IsGenerating, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                                    <StackPanel
											Orientation="Horizontal">
                                        <materialDesign:PackIcon
												Kind="Creation"
												VerticalAlignment="Center"
												Margin="0,0,8,0" />
                                        <TextBlock
												Text="Generate Prompt" />
                                    </StackPanel>
                                </Button>
                                <Button
									Style="{StaticResource SecondaryButtonStyle}"
									Command="{Binding ClearAllCommand}"
									ToolTip="Clear all data and start fresh">
                                    <StackPanel
											Orientation="Horizontal">
                                        <materialDesign:PackIcon
												Kind="DeleteSweep"
												VerticalAlignment="Center"
												Margin="0,0,8,0" />
                                        <TextBlock
												Text="Clear All" />
                                    </StackPanel>
                                </Button>
                                <Button
										Style="{StaticResource MaterialDesignOutlinedButton}"
										Command="{Binding GenerateStructuredPromptCommand}"
										IsEnabled="{Binding IsGenerating, Converter={StaticResource InverseBooleanToVisibilityConverter}}"
										ToolTip="Generate structured prompt with advanced features">
                                    <StackPanel
											Orientation="Horizontal">
                                        <materialDesign:PackIcon
												Kind="FileDocumentEdit"
												VerticalAlignment="Center"
												Margin="0,0,8,0" />
                                        <TextBlock
												Text="Structured" />
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                    <StackPanel
						Grid.Column="1"
						Margin="8,0,0,0">
                        <materialDesign:Card
							Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <DockPanel>
                                    <TextBlock
										Text="AI Enhancement"
										Style="{StaticResource SectionHeaderStyle}"
										DockPanel.Dock="Left" />

                                </DockPanel>
                                <TextBlock
									Text="{Binding AiEnhancementStatus}"
									Style="{StaticResource MaterialDesignBody2TextBlock}"
									Opacity="0.7"
									Margin="0,8,0,16"
									TextWrapping="Wrap" />
                                <Button
									Style="{StaticResource PrimaryButtonStyle}"
									HorizontalAlignment="Stretch"
									Command="{Binding EnhancePromptCommand}"
									IsEnabled="{Binding IsEnhancing, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                                    <StackPanel
											Orientation="Horizontal"
											HorizontalAlignment="Center">
                                        <materialDesign:PackIcon
												Kind="AutoAwesome"
												VerticalAlignment="Center"
												Margin="0,0,8,0" />
                                        <TextBlock
												Text="Enhance with AI" />
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </materialDesign:Card>
                        <materialDesign:Card
							Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <DockPanel>
                                    <TextBlock
										Text="Generated Prompt"
										Style="{StaticResource SectionHeaderStyle}"
										DockPanel.Dock="Left" />
                                    <Button
										Style="{StaticResource SecondaryButtonStyle}"
										HorizontalAlignment="Right"
										DockPanel.Dock="Right"
										Command="{Binding CopyPromptCommand}"
										CommandParameter="generated"
										Visibility="{Binding GeneratedPrompt, Converter={StaticResource BooleanToVisibilityConverter}}">
                                        <StackPanel
												Orientation="Horizontal">
                                            <materialDesign:PackIcon
													Kind="ContentCopy"
													VerticalAlignment="Center"
													Margin="0,0,4,0" />
                                            <TextBlock
													Text="Copy" />
                                        </StackPanel>
                                    </Button>
                                </DockPanel>
                                <TextBox
									IsReadOnly="True"
									AcceptsReturn="True"
									TextWrapping="Wrap"
									VerticalScrollBarVisibility="Auto"
									MinHeight="200"
									MaxHeight="300"
									Style="{StaticResource MaterialDesignOutlinedTextBox}"
									Text="{Binding GeneratedPrompt}" />
                            </StackPanel>
                        </materialDesign:Card>
                        <materialDesign:Card
							Style="{StaticResource CardStyle}"
							Visibility="{Binding EnhancedPrompt, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel>
                                <DockPanel>
                                    <TextBlock
										Text="AI Enhanced Prompt"
										Style="{StaticResource SectionHeaderStyle}"
										DockPanel.Dock="Left" />
                                    <Button
										Style="{StaticResource SecondaryButtonStyle}"
										HorizontalAlignment="Right"
										DockPanel.Dock="Right"
										Command="{Binding CopyPromptCommand}"
										CommandParameter="enhanced">
                                        <StackPanel
												Orientation="Horizontal">
                                            <materialDesign:PackIcon
													Kind="ContentCopy"
													VerticalAlignment="Center"
													Margin="0,0,4,0" />
                                            <TextBlock
													Text="Copy" />
                                        </StackPanel>
                                    </Button>
                                </DockPanel>
                                <TextBox
									IsReadOnly="True"
									AcceptsReturn="True"
									TextWrapping="Wrap"
									VerticalScrollBarVisibility="Auto"
									MinHeight="200"
									MaxHeight="300"
									Style="{StaticResource MaterialDesignOutlinedTextBox}"
									Text="{Binding EnhancedPrompt}" />
                            </StackPanel>
                        </materialDesign:Card>
                        <materialDesign:Card
							Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock
									Text="Recent History"
									Style="{StaticResource SectionHeaderStyle}" />
                                <ListBox
									MaxHeight="200"
									ScrollViewer.VerticalScrollBarVisibility="Auto"
									ItemsSource="{Binding History}">
                                    <ListBox.ItemTemplate>
                                        <DataTemplate>
                                            <Border
												Padding="8"
												Background="#00FFFFFF"
												BorderBrush="{DynamicResource MaterialDesignDivider}"
												BorderThickness="0,0,0,1">
                                                <Grid>
                                                <Grid.InputBindings>
                                                    <MouseBinding
														MouseAction="LeftClick"
														Command="{Binding DataContext.LoadFromHistoryCommand, RelativeSource={RelativeSource AncestorType={x:Type Window}}}"
														CommandParameter="{Binding}" />
                                                </Grid.InputBindings>
                                                <StackPanel>
                                                    <TextBlock
														FontWeight="Medium"
														TextTrimming="CharacterEllipsis"
														Text="{Binding ProjectIdea}" />
                                                    <TextBlock
														Opacity="0.6"
														FontSize="11"
														Text="{Binding ProjectType}" />
                                                    <TextBlock
														Opacity="0.6"
														FontSize="10"
														Text="{Binding CreatedAt, StringFormat=\{0:MMM dd\, yyyy HH:mm\}}" />
                                                </StackPanel>
                                                <!-- Delete Button -->
                                                <Button
                                                    Style="{StaticResource MaterialDesignIconButton}"
                                                    Width="20" Height="20"
                                                    Padding="0"
                                                    VerticalAlignment="Top"
                                                    HorizontalAlignment="Right"
                                                    Margin="0,-40,0,0"
                                                    Foreground="{DynamicResource MaterialDesignValidationErrorBrush}"
                                                    ToolTip="Delete this history item"
                                                    Command="{Binding DataContext.DeleteHistoryItemCommand, RelativeSource={RelativeSource AncestorType={x:Type Window}}}"
                                                    CommandParameter="{Binding}">
                                                    <materialDesign:PackIcon
                                                        Kind="Close"
                                                        Width="12" Height="12" />
                                                </Button>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ListBox.ItemTemplate>
                                </ListBox>
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                </Grid>
            </ScrollViewer>
            <materialDesign:ColorZone
				Grid.Row="2"
				Mode="PrimaryDark"
				Padding="16,8">
                <DockPanel>
                    <materialDesign:PackIcon
						Kind="Information"
						Width="16"
						Height="16"
						VerticalAlignment="Center"
						DockPanel.Dock="Left"
						Foreground="#FFFFFFFF" />
                    <TextBlock
						VerticalAlignment="Center"
						Margin="8,0,0,0"
						Foreground="#FFFFFFFF"
						Text="{Binding StatusMessage}" />
                    <StackPanel
						Orientation="Horizontal"
						HorizontalAlignment="Right"
						DockPanel.Dock="Right">
                        <ProgressBar
							IsIndeterminate="True"
							Width="100"
							Height="4"
							Margin="8,0"
							Visibility="{Binding IsGenerating, Converter={StaticResource BooleanToVisibilityConverter}}" />
                        <ProgressBar
							IsIndeterminate="True"
							Width="100"
							Height="4"
							Margin="8,0"
							Visibility="{Binding IsEnhancing, Converter={StaticResource BooleanToVisibilityConverter}}" />
                    </StackPanel>
                </DockPanel>
            </materialDesign:ColorZone>
        </Grid>
    </materialDesign:DialogHost>
</Window>
