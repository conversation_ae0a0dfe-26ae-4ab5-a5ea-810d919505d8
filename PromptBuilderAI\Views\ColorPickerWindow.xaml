<Window x:Class="PromptBuilderAI.Views.ColorPickerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="🎨 Custom Color Scheme Creator"
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,16">
            <TextBlock Text="🎨 Create Your Custom Color Scheme"
                       Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                       HorizontalAlignment="Center" Margin="0,0,0,8"/>
            <TextBlock Text="Choose colors that represent your project's personality"
                       Style="{StaticResource MaterialDesignBody2TextBlock}"
                       HorizontalAlignment="Center" Opacity="0.7"/>
        </StackPanel>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Scheme Name -->
                <materialDesign:Card Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <TextBlock Text="Scheme Information" Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Margin="0,0,0,8"/>
                        <TextBox materialDesign:HintAssist.Hint="Scheme Name (e.g., My Awesome Colors)"
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                 Text="{Binding SchemeName}" Margin="0,0,0,8"/>
                        <TextBox materialDesign:HintAssist.Hint="Description (optional)"
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                 Text="{Binding SchemeDescription}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Color Selection Grid -->
                <materialDesign:Card Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <TextBlock Text="Color Selection" Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Margin="0,0,0,16"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Primary Color -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,8,16">
                                <TextBlock Text="🔵 Primary Color" FontWeight="SemiBold" Margin="0,0,0,8"/>
                                <Border Height="60" CornerRadius="8" BorderThickness="2" BorderBrush="{DynamicResource MaterialDesignDivider}">
                                    <Border.Background>
                                        <SolidColorBrush Color="{Binding PrimaryColor}"/>
                                    </Border.Background>
                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Command="{Binding SelectPrimaryColorCommand}"
                                            Background="Transparent" Foreground="White" FontWeight="Bold">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Palette" Margin="0,0,4,0"/>
                                            <TextBlock Text="Choose"/>
                                        </StackPanel>
                                    </Button>
                                </Border>
                                <TextBlock Text="{Binding PrimaryColorHex}" HorizontalAlignment="Center"
                                           FontFamily="Consolas" FontSize="12" Margin="0,4,0,0"/>
                            </StackPanel>

                            <!-- Secondary Color -->
                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="8,0,0,16">
                                <TextBlock Text="🟢 Secondary Color" FontWeight="SemiBold" Margin="0,0,0,8"/>
                                <Border Height="60" CornerRadius="8" BorderThickness="2" BorderBrush="{DynamicResource MaterialDesignDivider}">
                                    <Border.Background>
                                        <SolidColorBrush Color="{Binding SecondaryColor}"/>
                                    </Border.Background>
                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Command="{Binding SelectSecondaryColorCommand}"
                                            Background="Transparent" Foreground="White" FontWeight="Bold">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Palette" Margin="0,0,4,0"/>
                                            <TextBlock Text="Choose"/>
                                        </StackPanel>
                                    </Button>
                                </Border>
                                <TextBlock Text="{Binding SecondaryColorHex}" HorizontalAlignment="Center"
                                           FontFamily="Consolas" FontSize="12" Margin="0,4,0,0"/>
                            </StackPanel>

                            <!-- Accent Color -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,8,16">
                                <TextBlock Text="🟠 Accent Color" FontWeight="SemiBold" Margin="0,0,0,8"/>
                                <Border Height="60" CornerRadius="8" BorderThickness="2" BorderBrush="{DynamicResource MaterialDesignDivider}">
                                    <Border.Background>
                                        <SolidColorBrush Color="{Binding AccentColor}"/>
                                    </Border.Background>
                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Command="{Binding SelectAccentColorCommand}"
                                            Background="Transparent" Foreground="White" FontWeight="Bold">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Palette" Margin="0,0,4,0"/>
                                            <TextBlock Text="Choose"/>
                                        </StackPanel>
                                    </Button>
                                </Border>
                                <TextBlock Text="{Binding AccentColorHex}" HorizontalAlignment="Center"
                                           FontFamily="Consolas" FontSize="12" Margin="0,4,0,0"/>
                            </StackPanel>

                            <!-- Success Color -->
                            <StackPanel Grid.Row="1" Grid.Column="1" Margin="8,0,0,16">
                                <TextBlock Text="✅ Success Color" FontWeight="SemiBold" Margin="0,0,0,8"/>
                                <Border Height="60" CornerRadius="8" BorderThickness="2" BorderBrush="{DynamicResource MaterialDesignDivider}">
                                    <Border.Background>
                                        <SolidColorBrush Color="{Binding SuccessColor}"/>
                                    </Border.Background>
                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Command="{Binding SelectSuccessColorCommand}"
                                            Background="Transparent" Foreground="White" FontWeight="Bold">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Palette" Margin="0,0,4,0"/>
                                            <TextBlock Text="Choose"/>
                                        </StackPanel>
                                    </Button>
                                </Border>
                                <TextBlock Text="{Binding SuccessColorHex}" HorizontalAlignment="Center"
                                           FontFamily="Consolas" FontSize="12" Margin="0,4,0,0"/>
                            </StackPanel>

                            <!-- Warning Color -->
                            <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,8,16">
                                <TextBlock Text="⚠️ Warning Color" FontWeight="SemiBold" Margin="0,0,0,8"/>
                                <Border Height="60" CornerRadius="8" BorderThickness="2" BorderBrush="{DynamicResource MaterialDesignDivider}">
                                    <Border.Background>
                                        <SolidColorBrush Color="{Binding WarningColor}"/>
                                    </Border.Background>
                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Command="{Binding SelectWarningColorCommand}"
                                            Background="Transparent" Foreground="Black" FontWeight="Bold">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Palette" Margin="0,0,4,0"/>
                                            <TextBlock Text="Choose"/>
                                        </StackPanel>
                                    </Button>
                                </Border>
                                <TextBlock Text="{Binding WarningColorHex}" HorizontalAlignment="Center"
                                           FontFamily="Consolas" FontSize="12" Margin="0,4,0,0"/>
                            </StackPanel>

                            <!-- Error Color -->
                            <StackPanel Grid.Row="2" Grid.Column="1" Margin="8,0,0,16">
                                <TextBlock Text="❌ Error Color" FontWeight="SemiBold" Margin="0,0,0,8"/>
                                <Border Height="60" CornerRadius="8" BorderThickness="2" BorderBrush="{DynamicResource MaterialDesignDivider}">
                                    <Border.Background>
                                        <SolidColorBrush Color="{Binding ErrorColor}"/>
                                    </Border.Background>
                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Command="{Binding SelectErrorColorCommand}"
                                            Background="Transparent" Foreground="White" FontWeight="Bold">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Palette" Margin="0,0,4,0"/>
                                            <TextBlock Text="Choose"/>
                                        </StackPanel>
                                    </Button>
                                </Border>
                                <TextBlock Text="{Binding ErrorColorHex}" HorizontalAlignment="Center"
                                           FontFamily="Consolas" FontSize="12" Margin="0,4,0,0"/>
                            </StackPanel>

                            <!-- Background Color -->
                            <StackPanel Grid.Row="3" Grid.Column="0" Margin="0,0,8,16">
                                <TextBlock Text="🏠 Background Color" FontWeight="SemiBold" Margin="0,0,0,8"/>
                                <Border Height="60" CornerRadius="8" BorderThickness="2" BorderBrush="{DynamicResource MaterialDesignDivider}">
                                    <Border.Background>
                                        <SolidColorBrush Color="{Binding BackgroundColor}"/>
                                    </Border.Background>
                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Command="{Binding SelectBackgroundColorCommand}"
                                            Background="Transparent" Foreground="Black" FontWeight="Bold">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Palette" Margin="0,0,4,0"/>
                                            <TextBlock Text="Choose"/>
                                        </StackPanel>
                                    </Button>
                                </Border>
                                <TextBlock Text="{Binding BackgroundColorHex}" HorizontalAlignment="Center"
                                           FontFamily="Consolas" FontSize="12" Margin="0,4,0,0"/>
                            </StackPanel>

                            <!-- Surface Color -->
                            <StackPanel Grid.Row="3" Grid.Column="1" Margin="8,0,0,16">
                                <TextBlock Text="📄 Surface Color" FontWeight="SemiBold" Margin="0,0,0,8"/>
                                <Border Height="60" CornerRadius="8" BorderThickness="2" BorderBrush="{DynamicResource MaterialDesignDivider}">
                                    <Border.Background>
                                        <SolidColorBrush Color="{Binding SurfaceColor}"/>
                                    </Border.Background>
                                    <Button Style="{StaticResource MaterialDesignFlatButton}"
                                            Command="{Binding SelectSurfaceColorCommand}"
                                            Background="Transparent" Foreground="Black" FontWeight="Bold">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Palette" Margin="0,0,4,0"/>
                                            <TextBlock Text="Choose"/>
                                        </StackPanel>
                                    </Button>
                                </Border>
                                <TextBlock Text="{Binding SurfaceColorHex}" HorizontalAlignment="Center"
                                           FontFamily="Consolas" FontSize="12" Margin="0,4,0,0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Quick Color Presets -->
                <materialDesign:Card Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <TextBlock Text="🎨 Quick Presets" Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Margin="0,0,0,8"/>
                        <TextBlock Text="Click on any preset to apply it as a starting point"
                                   Style="{StaticResource MaterialDesignBody2TextBlock}" Opacity="0.7" Margin="0,0,0,12"/>

                        <ItemsControl ItemsSource="{Binding ColorPresets}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel Orientation="Horizontal"/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Command="{Binding DataContext.ApplyPresetCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                            CommandParameter="{Binding}" Margin="0,0,8,8" Padding="8">
                                        <StackPanel Orientation="Horizontal">
                                            <Border Width="20" Height="20" CornerRadius="10" Margin="0,0,8,0">
                                                <Border.Background>
                                                    <SolidColorBrush Color="{Binding PrimaryColor}"/>
                                                </Border.Background>
                                            </Border>
                                            <TextBlock Text="{Binding Name}" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Live Preview -->
                <materialDesign:Card>
                    <StackPanel Margin="16">
                        <TextBlock Text="👁️ Live Preview" Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Margin="0,0,0,8"/>
                        <TextBlock Text="See how your colors work together"
                                   Style="{StaticResource MaterialDesignBody2TextBlock}" Opacity="0.7" Margin="0,0,0,12"/>

                        <!-- Preview Card -->
                        <Border CornerRadius="8" Padding="16" Margin="0,0,0,8">
                            <Border.Background>
                                <SolidColorBrush Color="{Binding BackgroundColor}"/>
                            </Border.Background>
                            <StackPanel>
                                <Border CornerRadius="4" Padding="12" Margin="0,0,0,8">
                                    <Border.Background>
                                        <SolidColorBrush Color="{Binding SurfaceColor}"/>
                                    </Border.Background>
                                    <StackPanel>
                                        <TextBlock Text="Sample Card" FontWeight="Bold" FontSize="16" Margin="0,0,0,8"/>
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                            <Button Content="Primary" Margin="0,0,8,0" Padding="8,4">
                                                <Button.Background>
                                                    <SolidColorBrush Color="{Binding PrimaryColor}"/>
                                                </Button.Background>
                                            </Button>
                                            <Button Content="Secondary" Margin="0,0,8,0" Padding="8,4">
                                                <Button.Background>
                                                    <SolidColorBrush Color="{Binding SecondaryColor}"/>
                                                </Button.Background>
                                            </Button>
                                            <Button Content="Accent" Padding="8,4">
                                                <Button.Background>
                                                    <SolidColorBrush Color="{Binding AccentColor}"/>
                                                </Button.Background>
                                            </Button>
                                        </StackPanel>
                                        <StackPanel Orientation="Horizontal">
                                            <Border CornerRadius="2" Padding="4,2" Margin="0,0,4,0">
                                                <Border.Background>
                                                    <SolidColorBrush Color="{Binding SuccessColor}"/>
                                                </Border.Background>
                                                <TextBlock Text="Success" Foreground="White" FontSize="10"/>
                                            </Border>
                                            <Border CornerRadius="2" Padding="4,2" Margin="0,0,4,0">
                                                <Border.Background>
                                                    <SolidColorBrush Color="{Binding WarningColor}"/>
                                                </Border.Background>
                                                <TextBlock Text="Warning" Foreground="Black" FontSize="10"/>
                                            </Border>
                                            <Border CornerRadius="2" Padding="4,2">
                                                <Border.Background>
                                                    <SolidColorBrush Color="{Binding ErrorColor}"/>
                                                </Border.Background>
                                                <TextBlock Text="Error" Foreground="White" FontSize="10"/>
                                            </Border>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- Footer Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
            <Button Content="Cancel" Style="{StaticResource MaterialDesignOutlinedButton}"
                    Command="{Binding CancelCommand}" Margin="0,0,8,0" Padding="16,8"/>
            <Button Content="💾 Save Color Scheme" Style="{StaticResource MaterialDesignRaisedButton}"
                    Command="{Binding SaveSchemeCommand}" Padding="16,8"/>
        </StackPanel>
    </Grid>
</Window>
