<Application
	x:Class="PromptBuilderAI.App"
	xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
	xmlns:converters="clr-namespace:PromptBuilderAI.Converters"
	xmlns:promptbuilderai="clr-namespace:PromptBuilderAI"
	Startup="Application_Startup">
	<Application.Resources>
		<ResourceDictionary>
			<ResourceDictionary.MergedDictionaries>
				<materialDesign:BundledTheme
					BaseTheme="Light"
					PrimaryColor="DeepPurple"
					SecondaryColor="Lime" />
				<ResourceDictionary
					Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesign3.Defaults.xaml" />
			</ResourceDictionary.MergedDictionaries>

			<!-- Custom Dark Theme Colors -->
			<SolidColorBrush x:Key="CustomDarkBackground" Color="#181818" />
			<SolidColorBrush x:Key="CustomDarkSurface" Color="#202020" />
			<SolidColorBrush x:Key="CustomDarkCard" Color="#252525" />
			<SolidColorBrush x:Key="CustomDarkText" Color="#E0E0E0" />
			<SolidColorBrush x:Key="CustomDarkTextSecondary" Color="#B0B0B0" />
			<converters:BooleanToVisibilityConverter
				x:Key="BooleanToVisibilityConverter" />
			<converters:InverseBooleanToVisibilityConverter
				x:Key="InverseBooleanToVisibilityConverter" />
			<converters:ColorToBrushConverter
				x:Key="ColorToBrushConverter" />
			<Style
				x:Key="SectionHeaderStyle"
				TargetType="{x:Type TextBlock}"
				BasedOn="{StaticResource MaterialDesignHeadline6TextBlock}">
				<Setter
					Property="Margin"
					Value="0,16,0,8" />
				<Setter
					Property="Foreground"
					Value="{DynamicResource PrimaryHueMidBrush}" />
			</Style>
			<Style
				x:Key="SubHeaderStyle"
				TargetType="{x:Type TextBlock}"
				BasedOn="{StaticResource MaterialDesignSubtitle1TextBlock}">
				<Setter
					Property="Margin"
					Value="0,8,0,4" />
				<Setter
					Property="Foreground"
					Value="{DynamicResource MaterialDesignBody}" />
			</Style>
			<Style
				x:Key="CardStyle"
				TargetType="{x:Type materialDesign:Card}">
				<Setter
					Property="Margin"
					Value="8" />
				<Setter
					Property="Padding"
					Value="16" />
				<Setter
					Property="materialDesign:ElevationAssist.Elevation"
					Value="Dp2" />
			</Style>
			<Style
				x:Key="PrimaryButtonStyle"
				TargetType="{x:Type Button}"
				BasedOn="{StaticResource MaterialDesignRaisedButton}">
				<Setter
					Property="Margin"
					Value="4" />
				<Setter
					Property="Padding"
					Value="16,8" />
				<Setter
					Property="materialDesign:ButtonAssist.CornerRadius"
					Value="4" />
			</Style>
			<Style
				x:Key="SecondaryButtonStyle"
				TargetType="{x:Type Button}"
				BasedOn="{StaticResource MaterialDesignOutlinedButton}">
				<Setter
					Property="Margin"
					Value="4" />
				<Setter
					Property="Padding"
					Value="16,8" />
				<Setter
					Property="materialDesign:ButtonAssist.CornerRadius"
					Value="4" />
			</Style>
		</ResourceDictionary>
	</Application.Resources>
</Application>
