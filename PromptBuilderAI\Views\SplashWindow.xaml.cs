using System;
using System.Windows;
using System.Windows.Media.Animation;
using Microsoft.Extensions.DependencyInjection;
using PromptBuilderAI.ViewModels;

namespace PromptBuilderAI.Views
{
    /// <summary>
    /// Simple Splash Window for Prompt Builder AI
    /// </summary>
    public partial class SplashWindow : Window
    {
        public SplashWindow()
        {
            InitializeComponent();
            
            // Set up loading completion handler
            LoadingSplashControl.OnLoadingCompleted = OnLoadingCompleted;
            
            // Start fade in animation
            Loaded += SplashWindow_Loaded;
        }

        private void SplashWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // Fade in animation
            var fadeIn = new DoubleAnimation
            {
                From = 0.0,
                To = 1.0,
                Duration = TimeSpan.FromMilliseconds(500)
            };
            
            this.BeginAnimation(OpacityProperty, fadeIn);
        }

        private void OnLoadingCompleted()
        {
            // Wait a moment then fade out and show main window
            var timer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(500)
            };
            
            timer.Tick += (s, e) =>
            {
                timer.Stop();
                FadeOutAndShowMainWindow();
            };
            
            timer.Start();
        }

        private void FadeOutAndShowMainWindow()
        {
            // Create fade out animation
            var fadeOut = new DoubleAnimation
            {
                From = 1.0,
                To = 0.0,
                Duration = TimeSpan.FromMilliseconds(500)
            };

            fadeOut.Completed += (s, e) =>
            {
                // Get the App instance to access ServiceProvider
                var app = (App)Application.Current;
                
                // Show main window with proper DI
                var mainWindow = new MainWindow
                {
                    DataContext = app._serviceProvider.GetRequiredService<MainViewModel>()
                };
                
                Application.Current.MainWindow = mainWindow;
                mainWindow.Visibility = Visibility.Visible;
                mainWindow.WindowState = WindowState.Normal;
                mainWindow.Show();
                mainWindow.Activate();
                mainWindow.Focus();
                
                // Close splash screen
                this.Close();
            };

            this.BeginAnimation(OpacityProperty, fadeOut);
        }
    }
}
