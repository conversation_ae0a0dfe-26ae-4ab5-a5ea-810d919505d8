using System;
using System.Collections.Generic;

namespace PromptBuilderAI.Licensing.Models
{
    public enum LicenseType
    {
        Trial,
        TimeLimited,
        Lifetime
    }

    public enum LicenseStatus
    {
        NotActivated,
        Active,
        Expired,
        Invalid
    }

    public class LicenseInfo
    {
        public string SerialNumber { get; set; } = "";
        public LicenseType Type { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string ActivationId { get; set; } = "";
        public string MachineFingerprint { get; set; } = "";
        public DateTime ActivationDate { get; set; }
        
        public bool IsValid { get; set; }
        public bool IsLifetime => Type == LicenseType.Lifetime;
        public bool IsExpired => ExpiryDate.HasValue && DateTime.Now > ExpiryDate.Value;
        
        public LicenseStatus Status
        {
            get
            {
                if (!IsValid) return LicenseStatus.Invalid;
                if (IsExpired) return LicenseStatus.Expired;
                return LicenseStatus.Active;
            }
        }
        
        public int DaysRemaining
        {
            get
            {
                if (IsLifetime) return int.MaxValue;
                if (!ExpiryDate.HasValue) return 0;
                var remaining = (ExpiryDate.Value - DateTime.Now).Days;
                return Math.Max(0, remaining);
            }
        }
        
        public List<string> EnabledFeatures { get; set; } = new();
    }

    public class ActivationRequest
    {
        public string SerialNumber { get; set; } = "";
        public string MachineFingerprint { get; set; } = "";
    }

    public class ActivationResult
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } = "";
        public LicenseInfo? LicenseInfo { get; set; }
        public string ActivationId { get; set; } = "";
    }
}