<Window x:Class="PromptBuilderAI.Licensing.Views.ActivationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Activate Prompt Builder AI" 
        Height="500" Width="600"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,30">
            <materialDesign:PackIcon Kind="Key" 
                                   Width="64" Height="64"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                   HorizontalAlignment="Center"
                                   Margin="0,0,0,15"/>
            
            <TextBlock Text="Activate Prompt Builder AI" 
                      FontSize="24" FontWeight="Bold" 
                      HorizontalAlignment="Center"
                      Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            
            <TextBlock Text="Enter your license key to unlock all features" 
                      FontSize="14" 
                      HorizontalAlignment="Center"
                      Margin="0,5,0,0"
                      Opacity="0.7"/>
        </StackPanel>        <!-- Serial Input -->
        <materialDesign:Card Grid.Row="1" Padding="20" Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="License Key:" FontWeight="SemiBold" Margin="0,0,0,10"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="10"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox x:Name="SerialPart1" Grid.Column="0" 
                           MaxLength="4" 
                           FontFamily="Consolas" FontSize="14"
                           materialDesign:HintAssist.Hint="XXXX"
                           TextChanged="SerialPart_TextChanged"/>
                    
                    <TextBlock Grid.Column="1" Text="-" VerticalAlignment="Center" HorizontalAlignment="Center" FontWeight="Bold"/>
                    
                    <TextBox x:Name="SerialPart2" Grid.Column="2" 
                           MaxLength="4" 
                           FontFamily="Consolas" FontSize="14"
                           materialDesign:HintAssist.Hint="XXXX"
                           TextChanged="SerialPart_TextChanged"/>
                    
                    <TextBlock Grid.Column="3" Text="-" VerticalAlignment="Center" HorizontalAlignment="Center" FontWeight="Bold"/>
                    
                    <TextBox x:Name="SerialPart3" Grid.Column="4" 
                           MaxLength="4" 
                           FontFamily="Consolas" FontSize="14"
                           materialDesign:HintAssist.Hint="XXXX"
                           TextChanged="SerialPart_TextChanged"/>
                    
                    <TextBlock Grid.Column="5" Text="-" VerticalAlignment="Center" HorizontalAlignment="Center" FontWeight="Bold"/>
                    
                    <TextBox x:Name="SerialPart4" Grid.Column="6" 
                           MaxLength="4" 
                           FontFamily="Consolas" FontSize="14"
                           materialDesign:HintAssist.Hint="XXXX"
                           TextChanged="SerialPart_TextChanged"/>
                </Grid>
            </StackPanel>
        </materialDesign:Card>        <!-- Status Display -->
        <materialDesign:Card Grid.Row="2" Padding="20" Margin="0,0,0,20" x:Name="StatusCard" Visibility="Collapsed">
            <StackPanel>
                <TextBlock x:Name="StatusText" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                <TextBlock x:Name="StatusDetails" Margin="0,5,0,0" HorizontalAlignment="Center" Opacity="0.7"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
            <Button x:Name="ActivateButton" 
                  Content="Activate License"
                  Style="{StaticResource MaterialDesignRaisedButton}"
                  Padding="30,12"
                  Margin="0,0,15,0"
                  Click="ActivateButton_Click"
                  IsEnabled="False"/>
            
            <Button Content="Start Trial"
                  Style="{StaticResource MaterialDesignOutlinedButton}"
                  Padding="30,12"
                  Margin="0,0,15,0"
                  Click="StartTrialButton_Click"/>
            
            <Button Content="Buy License"
                  Style="{StaticResource MaterialDesignOutlinedButton}"
                  Padding="30,12"
                  Click="BuyLicenseButton_Click"/>
        </StackPanel>

        <!-- Features Comparison -->
        <materialDesign:Card Grid.Row="4" Padding="20">
            <StackPanel>
                <TextBlock Text="Feature Comparison" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,15"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="100"/>
                    </Grid.ColumnDefinitions>
                    
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>                    <!-- Headers -->
                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Features" FontWeight="SemiBold"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" Text="Trial" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                    <TextBlock Grid.Row="0" Grid.Column="2" Text="Licensed" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                    
                    <!-- Feature Rows -->
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="Basic Prompt Generation" Margin="0,5"/>
                    <materialDesign:PackIcon Grid.Row="1" Grid.Column="1" Kind="Check" Foreground="Green" HorizontalAlignment="Center"/>
                    <materialDesign:PackIcon Grid.Row="1" Grid.Column="2" Kind="Check" Foreground="Green" HorizontalAlignment="Center"/>
                    
                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Color Selection" Margin="0,5"/>
                    <materialDesign:PackIcon Grid.Row="2" Grid.Column="1" Kind="Check" Foreground="Green" HorizontalAlignment="Center"/>
                    <materialDesign:PackIcon Grid.Row="2" Grid.Column="2" Kind="Check" Foreground="Green" HorizontalAlignment="Center"/>
                    
                    <TextBlock Grid.Row="3" Grid.Column="0" Text="AI Enhancement" Margin="0,5"/>
                    <materialDesign:PackIcon Grid.Row="3" Grid.Column="1" Kind="Close" Foreground="Red" HorizontalAlignment="Center"/>
                    <materialDesign:PackIcon Grid.Row="3" Grid.Column="2" Kind="Check" Foreground="Green" HorizontalAlignment="Center"/>
                    
                    <TextBlock Grid.Row="4" Grid.Column="0" Text="Advanced Templates" Margin="0,5"/>
                    <materialDesign:PackIcon Grid.Row="4" Grid.Column="1" Kind="Close" Foreground="Red" HorizontalAlignment="Center"/>
                    <materialDesign:PackIcon Grid.Row="4" Grid.Column="2" Kind="Check" Foreground="Green" HorizontalAlignment="Center"/>
                    
                    <TextBlock Grid.Row="5" Grid.Column="0" Text="Export Options" Margin="0,5"/>
                    <materialDesign:PackIcon Grid.Row="5" Grid.Column="1" Kind="Close" Foreground="Red" HorizontalAlignment="Center"/>
                    <materialDesign:PackIcon Grid.Row="5" Grid.Column="2" Kind="Check" Foreground="Green" HorizontalAlignment="Center"/>
                </Grid>
            </StackPanel>
        </materialDesign:Card>

        <!-- Footer -->
        <StackPanel Grid.Row="5" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Content="Close" 
                  Style="{StaticResource MaterialDesignFlatButton}"
                  Click="CloseButton_Click"
                  Padding="20,8"/>
        </StackPanel>
    </Grid>
</Window>