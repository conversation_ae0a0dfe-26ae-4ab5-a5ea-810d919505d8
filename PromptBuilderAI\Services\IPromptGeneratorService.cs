using System;
using System.Collections.Generic;
using System.Windows.Media;
using PromptBuilderAI.Models;

namespace PromptBuilderAI.Services
{
	public interface IPromptGeneratorService
	{
		string GeneratePrompt(string projectIdea, ProjectType projectType, List<ProgrammingLanguage> selectedLanguages, List<Framework> selectedFrameworks, List<ProjectLanguage> selectedProjectLanguages, ThemeSupport? themeSupport, ColorPalette? colorPalette);

		// New structured prompt methods
		string GenerateStructuredPrompt(string projectIdea, ProjectType projectType, StructuredPromptConfig config, List<ProgrammingLanguage> selectedLanguages, List<Framework> selectedFrameworks, List<ProjectLanguage> selectedProjectLanguages, ThemeSupport? themeSupport, ColorPalette? colorPalette);
		StructuredPromptConfig GetDefaultStructureConfig(PromptStructureType structureType);
		List<PromptSection> GetDefaultSections(ProjectType projectType, PromptStructureType structureType);
		List<ProjectRequirement> GetDefaultRequirements(ProjectType projectType);
		List<ProjectConstraint> GetDefaultConstraints(ProjectType projectType);
		List<SuccessCriteria> GetDefaultSuccessCriteria(ProjectType projectType);
	}

	/// <summary>
	/// Interface for color palette and theme management
	/// </summary>
	public interface IColorPaletteService
	{
		List<ColorScheme> GetAvailableColorSchemes();
		ColorScheme GetColorScheme(ColorPalette palette);
		ColorScheme CreateCustomColorScheme(string name, Dictionary<string, Color> colors);
		string GenerateColorPromptSection(ColorPalette palette, ProjectType projectType);
		string GenerateCssVariables(ColorScheme scheme);
		string GenerateFrameworkColorTokens(ColorScheme scheme, string framework);
		ColorAccessibility CheckColorAccessibility(Color foreground, Color background);
		List<ColorScheme> GetColorSchemesByCategory(ColorCategory category);
		ColorScheme GenerateColorSchemeFromImage(string imagePath);
		bool SaveCustomColorScheme(ColorScheme scheme);
		bool DeleteCustomColorScheme(string schemeName);
	}
}
