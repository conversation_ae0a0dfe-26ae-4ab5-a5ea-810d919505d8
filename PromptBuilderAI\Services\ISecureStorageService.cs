#nullable enable
using System.Threading.Tasks;

namespace PromptBuilderAI.Services
{
    /// <summary>
    /// Service for secure storage and retrieval of sensitive data like API keys
    /// </summary>
    public interface ISecureStorageService
    {
        /// <summary>
        /// Securely stores an API key with encryption
        /// </summary>
        /// <param name="key">The key identifier</param>
        /// <param name="value">The API key to store</param>
        Task StoreSecureValueAsync(string key, string value);

        /// <summary>
        /// Retrieves and decrypts a stored API key
        /// </summary>
        /// <param name="key">The key identifier</param>
        /// <returns>The decrypted API key or null if not found</returns>
        Task<string?> GetSecureValueAsync(string key);

        /// <summary>
        /// Removes a stored API key
        /// </summary>
        /// <param name="key">The key identifier</param>
        Task RemoveSecureValueAsync(string key);

        /// <summary>
        /// Checks if a secure value exists
        /// </summary>
        /// <param name="key">The key identifier</param>
        /// <returns>True if the key exists</returns>
        Task<bool> HasSecureValueAsync(string key);
    }
}
