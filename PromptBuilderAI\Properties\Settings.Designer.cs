using System;
using System.CodeDom.Compiler;
using System.Configuration;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace PromptBuilderAI.Properties
{
	[CompilerGenerated]
	[GeneratedCode("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "17.0.0.0")]
	internal sealed partial class Settings : ApplicationSettingsBase
	{
		public static Settings Default
		{
			get
			{
				return Settings.defaultInstance;
			}
		}
		[UserScopedSetting]
		[DebuggerNonUserCode]
		[DefaultSettingValue("")]
		public string ApiKey
		{
			get
			{
				return (string)this["ApiKey"];
			}
			set
			{
				this["ApiKey"] = value;
			}
		}
		[UserScopedSetting]
		[DebuggerNonUserCode]
		[DefaultSettingValue("True")]
		public bool AutoSavePrompts
		{
			get
			{
				return (bool)this["AutoSavePrompts"];
			}
			set
			{
				this["AutoSavePrompts"] = value;
			}
		}
		[UserScopedSetting]
		[DebuggerNonUserCode]
		[DefaultSettingValue("True")]
		public bool AutoLoadModels
		{
			get
			{
				return (bool)this["AutoLoadModels"];
			}
			set
			{
				this["AutoLoadModels"] = value;
			}
		}
		[UserScopedSetting]
		[DebuggerNonUserCode]
		[DefaultSettingValue("True")]
		public bool ShowNotifications
		{
			get
			{
				return (bool)this["ShowNotifications"];
			}
			set
			{
				this["ShowNotifications"] = value;
			}
		}
		[UserScopedSetting]
		[DebuggerNonUserCode]
		[DefaultSettingValue("True")]
		public bool RememberWindowState
		{
			get
			{
				return (bool)this["RememberWindowState"];
			}
			set
			{
				this["RememberWindowState"] = value;
			}
		}
		[UserScopedSetting]
		[DebuggerNonUserCode]
		[DefaultSettingValue("2")]
		public int DefaultProjectType
		{
			get
			{
				return (int)this["DefaultProjectType"];
			}
			set
			{
				this["DefaultProjectType"] = value;
			}
		}
		[UserScopedSetting]
		[DebuggerNonUserCode]
		[DefaultSettingValue("0")]
		public int ThemeMode
		{
			get
			{
				return (int)this["ThemeMode"];
			}
			set
			{
				this["ThemeMode"] = value;
			}
		}
		[UserScopedSetting]
		[DebuggerNonUserCode]
		[DefaultSettingValue("Deep Purple")]
		public string PrimaryColor
		{
			get
			{
				return (string)this["PrimaryColor"];
			}
			set
			{
				this["PrimaryColor"] = value;
			}
		}
		[UserScopedSetting]
		[DebuggerNonUserCode]
		[DefaultSettingValue("openai/gpt-4")]
		public string SelectedModel
		{
			get
			{
				return (string)this["SelectedModel"];
			}
			set
			{
				this["SelectedModel"] = value;
			}
		}
		[UserScopedSetting]
		[DebuggerNonUserCode]
		[DefaultSettingValue("False")]
		public bool IsDarkMode
		{
			get
			{
				return (bool)this["IsDarkMode"];
			}
			set
			{
				this["IsDarkMode"] = value;
			}
		}
		private static Settings defaultInstance = (Settings)SettingsBase.Synchronized(new Settings());
	}
}
