#nullable enable
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
namespace PromptBuilderAI.Models
{
	public class Framework : INotifyPropertyChanged
	{
		public string Name { get; set; } = string.Empty;

		private bool _isSelected;
		public bool IsSelected
		{
			get => _isSelected;
			set
			{
				_isSelected = value;
				PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(IsSelected)));
			}
		}

		public string Language { get; set; } = string.Empty; // Associated language
		public List<string> RequiredPackages { get; set; } = new List<string>(); // Required packages/libraries

		public event PropertyChangedEventHandler? PropertyChanged;
	}

	public class Library : INotifyPropertyChanged
	{
		public string Name { get; set; } = string.Empty;

		private bool _isSelected;
		public bool IsSelected
		{
			get => _isSelected;
			set
			{
				_isSelected = value;
				PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(IsSelected)));
				// Notify parent group if available
				ParentGroup?.NotifySelectionChanged();
			}
		}

		public string Category { get; set; } = string.Empty; // UI, Database, Testing, etc.
		public string Framework { get; set; } = string.Empty; // Associated framework
		public string Language { get; set; } = string.Empty; // Associated language
		public string Description { get; set; } = string.Empty; // Brief description
		public string PackageManager { get; set; } = string.Empty; // NuGet, npm, pip, etc.
		public string InstallCommand { get; set; } = string.Empty; // Installation command
		public string IconKind { get; set; } = "Package"; // Material Design icon
		public int Popularity { get; set; } = 0; // 1-5 stars
		public string ImagePath { get; set; } = string.Empty; // Path to library thumbnail
		public string FriendlyName { get; set; } = string.Empty; // User-friendly name
		public string UserDescription { get; set; } = string.Empty; // Simple description for users

		// Reference to parent group for notifications
		public LibraryGroup? ParentGroup { get; set; }

		public event PropertyChangedEventHandler? PropertyChanged;
	}

	public class LibraryGroup : INotifyPropertyChanged
	{
		public string CategoryName { get; set; } = string.Empty;
		public string CategoryIcon { get; set; } = "Package";
		public ObservableCollection<Library> Libraries { get; set; } = new ObservableCollection<Library>();
		public int TotalCount => Libraries.Count;
		public int SelectedCount => Libraries.Count(l => l.IsSelected);

		public event PropertyChangedEventHandler? PropertyChanged;

		public void NotifySelectionChanged()
		{
			PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(SelectedCount)));
		}
	}

	/// <summary>
	/// Represents a structured section of the prompt
	/// </summary>
	public class PromptSection
	{
		public string Title { get; set; } = string.Empty;
		public string Content { get; set; } = string.Empty;
		public PriorityLevel Priority { get; set; } = PriorityLevel.Medium;
		public int Order { get; set; } = 0;
		public bool IsRequired { get; set; } = true;
		public string Icon { get; set; } = "Information";
		public List<string> SubSections { get; set; } = new List<string>();
	}

	/// <summary>
	/// Represents a project requirement with priority and constraints
	/// </summary>
	public class ProjectRequirement
	{
		public string Title { get; set; } = string.Empty;
		public string Description { get; set; } = string.Empty;
		public PriorityLevel Priority { get; set; } = PriorityLevel.Medium;
		public bool IsMandatory { get; set; } = false;
		public List<string> AcceptanceCriteria { get; set; } = new List<string>();
		public string Category { get; set; } = string.Empty;
	}

	/// <summary>
	/// Represents a project constraint
	/// </summary>
	public class ProjectConstraint
	{
		public string Title { get; set; } = string.Empty;
		public string Description { get; set; } = string.Empty;
		public ConstraintType Type { get; set; } = ConstraintType.Technical;
		public PriorityLevel Impact { get; set; } = PriorityLevel.Medium;
		public string Justification { get; set; } = string.Empty;
	}

	/// <summary>
	/// Represents success criteria for the project
	/// </summary>
	public class SuccessCriteria
	{
		public string Title { get; set; } = string.Empty;
		public string Description { get; set; } = string.Empty;
		public string MeasurementMethod { get; set; } = string.Empty;
		public string TargetValue { get; set; } = string.Empty;
		public PriorityLevel Priority { get; set; } = PriorityLevel.Medium;
	}

	/// <summary>
	/// Represents the complete structured prompt configuration
	/// </summary>
	public class StructuredPromptConfig
	{
		public PromptStructureType StructureType { get; set; } = PromptStructureType.Standard;
		public List<PromptSection> Sections { get; set; } = new List<PromptSection>();
		public List<ProjectRequirement> Requirements { get; set; } = new List<ProjectRequirement>();
		public List<ProjectConstraint> Constraints { get; set; } = new List<ProjectConstraint>();
		public List<SuccessCriteria> SuccessCriteria { get; set; } = new List<SuccessCriteria>();
		public bool IncludeExamples { get; set; } = true;
		public bool IncludeConstraints { get; set; } = true;
		public bool IncludeSuccessCriteria { get; set; } = true;
		public int MaxLength { get; set; } = 0; // 0 = no limit
		public string CustomInstructions { get; set; } = string.Empty;
	}
}
