using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using PromptBuilderAI.Models;
using Newtonsoft.Json;

namespace PromptBuilderAI.Services
{
    /// <summary>
    /// Service for managing color palettes and schemes
    /// </summary>
    public class ColorPaletteService : IColorPaletteService
    {
        private readonly string _customSchemesPath;
        private readonly List<ColorScheme> _predefinedSchemes;

        public ColorPaletteService()
        {
            _customSchemesPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "PromptBuilderAI",
                "ColorSchemes");
            
            Directory.CreateDirectory(_customSchemesPath);
            _predefinedSchemes = InitializePredefinedSchemes();
        }

        /// <summary>
        /// Gets all available color schemes (predefined + custom)
        /// </summary>
        public List<ColorScheme> GetAvailableColorSchemes()
        {
            var schemes = new List<ColorScheme>(_predefinedSchemes);
            
            // Load custom schemes
            var customSchemes = LoadCustomColorSchemes();
            schemes.AddRange(customSchemes);
            
            return schemes;
        }

        /// <summary>
        /// Gets a specific color scheme by palette type
        /// </summary>
        public ColorScheme GetColorScheme(ColorPalette palette)
        {
            return _predefinedSchemes.FirstOrDefault(s => s.PaletteType == palette) 
                   ?? GetDefaultColorScheme();
        }

        /// <summary>
        /// Creates a custom color scheme
        /// </summary>
        public ColorScheme CreateCustomColorScheme(string name, Dictionary<string, Color> colors)
        {
            var scheme = new ColorScheme
            {
                Name = name,
                PaletteType = ColorPalette.Custom,
                Description = "Custom color scheme created by user"
            };

            // Map colors to scheme properties
            if (colors.ContainsKey("Primary")) scheme.Primary = colors["Primary"];
            if (colors.ContainsKey("Secondary")) scheme.Secondary = colors["Secondary"];
            if (colors.ContainsKey("Accent")) scheme.Accent = colors["Accent"];
            if (colors.ContainsKey("Background")) scheme.Background = colors["Background"];
            if (colors.ContainsKey("Surface")) scheme.Surface = colors["Surface"];
            if (colors.ContainsKey("Success")) scheme.Success = colors["Success"];
            if (colors.ContainsKey("Warning")) scheme.Warning = colors["Warning"];
            if (colors.ContainsKey("Error")) scheme.Error = colors["Error"];

            // Generate CSS variables
            scheme.CssVariables = GenerateCssVariablesDict(scheme);
            
            return scheme;
        }

        /// <summary>
        /// Generates color-specific prompt section
        /// </summary>
        public string GenerateColorPromptSection(ColorPalette palette, ProjectType projectType)
        {
            if (palette == ColorPalette.None) return string.Empty;

            var scheme = GetColorScheme(palette);
            var section = new System.Text.StringBuilder();

            section.AppendLine("## 🎨 Color Palette & Design System");
            section.AppendLine();
            section.AppendLine($"**Selected Palette:** {palette}");
            section.AppendLine($"**Description:** {scheme.Description}");
            section.AppendLine();

            // Add color specifications based on project type
            switch (projectType)
            {
                case ProjectType.WebApp:
                    section.AppendLine("**Web Color Implementation:**");
                    section.AppendLine("- Use CSS custom properties (variables) for consistent theming");
                    section.AppendLine("- Implement dark/light mode support");
                    section.AppendLine("- Ensure WCAG AA accessibility compliance");
                    section.AppendLine("- Use semantic color naming (primary, secondary, success, etc.)");
                    break;

                case ProjectType.MobileApp:
                    section.AppendLine("**Mobile Color Implementation:**");
                    section.AppendLine("- Follow platform-specific color guidelines (Material Design/Human Interface)");
                    section.AppendLine("- Implement adaptive colors for system theme changes");
                    section.AppendLine("- Consider color blindness accessibility");
                    section.AppendLine("- Use appropriate contrast ratios for mobile viewing");
                    break;

                case ProjectType.DesktopApp:
                    section.AppendLine("**Desktop Color Implementation:**");
                    section.AppendLine("- Integrate with system theme preferences");
                    section.AppendLine("- Provide user customization options");
                    section.AppendLine("- Ensure readability in various lighting conditions");
                    section.AppendLine("- Support high contrast modes for accessibility");
                    break;
            }

            section.AppendLine();
            section.AppendLine("**Color Specifications:**");
            section.AppendLine($"- Primary: {ColorToHex(scheme.Primary)}");
            section.AppendLine($"- Secondary: {ColorToHex(scheme.Secondary)}");
            section.AppendLine($"- Accent: {ColorToHex(scheme.Accent)}");
            section.AppendLine($"- Success: {ColorToHex(scheme.Success)}");
            section.AppendLine($"- Warning: {ColorToHex(scheme.Warning)}");
            section.AppendLine($"- Error: {ColorToHex(scheme.Error)}");
            section.AppendLine();

            return section.ToString();
        }

        /// <summary>
        /// Generates CSS variables from color scheme
        /// </summary>
        public string GenerateCssVariables(ColorScheme scheme)
        {
            var css = new System.Text.StringBuilder();
            css.AppendLine(":root {");
            
            foreach (var variable in scheme.CssVariables)
            {
                css.AppendLine($"  --{variable.Key}: {variable.Value};");
            }
            
            css.AppendLine("}");
            return css.ToString();
        }

        /// <summary>
        /// Generates framework-specific color tokens
        /// </summary>
        public string GenerateFrameworkColorTokens(ColorScheme scheme, string framework)
        {
            return framework.ToLower() switch
            {
                "tailwind" => GenerateTailwindConfig(scheme),
                "bootstrap" => GenerateBootstrapVariables(scheme),
                "material" => GenerateMaterialTokens(scheme),
                "antd" => GenerateAntDesignTokens(scheme),
                _ => GenerateCssVariables(scheme)
            };
        }

        /// <summary>
        /// Checks color accessibility compliance
        /// </summary>
        public ColorAccessibility CheckColorAccessibility(Color foreground, Color background)
        {
            var contrastRatio = CalculateContrastRatio(foreground, background);
            
            return new ColorAccessibility
            {
                ContrastRatio = contrastRatio,
                Level = GetAccessibilityLevel(contrastRatio),
                IsReadable = contrastRatio >= 4.5,
                Recommendation = GetAccessibilityRecommendation(contrastRatio)
            };
        }

        /// <summary>
        /// Gets color schemes by category
        /// </summary>
        public List<ColorScheme> GetColorSchemesByCategory(ColorCategory category)
        {
            return GetAvailableColorSchemes()
                .Where(s => GetSchemeCategory(s) == category)
                .ToList();
        }

        /// <summary>
        /// Generates color scheme from image (basic implementation)
        /// </summary>
        public ColorScheme GenerateColorSchemeFromImage(string imagePath)
        {
            try
            {
                var bitmap = new BitmapImage(new Uri(imagePath));
                // This is a simplified implementation
                // In a real scenario, you'd use color extraction algorithms
                
                var scheme = new ColorScheme
                {
                    Name = "Generated from Image",
                    PaletteType = ColorPalette.Custom,
                    Description = $"Color scheme extracted from {Path.GetFileName(imagePath)}",
                    Primary = Colors.Blue,
                    Secondary = Colors.Gray,
                    Accent = Colors.Orange,
                    Background = Colors.White,
                    Surface = Colors.LightGray
                };

                scheme.CssVariables = GenerateCssVariablesDict(scheme);
                return scheme;
            }
            catch
            {
                return GetDefaultColorScheme();
            }
        }

        /// <summary>
        /// Saves custom color scheme
        /// </summary>
        public bool SaveCustomColorScheme(ColorScheme scheme)
        {
            try
            {
                var filePath = Path.Combine(_customSchemesPath, $"{scheme.Name}.json");
                var json = JsonConvert.SerializeObject(scheme, Formatting.Indented);
                File.WriteAllText(filePath, json);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Deletes custom color scheme
        /// </summary>
        public bool DeleteCustomColorScheme(string schemeName)
        {
            try
            {
                var filePath = Path.Combine(_customSchemesPath, $"{schemeName}.json");
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        #region Private Methods

        private List<ColorScheme> InitializePredefinedSchemes()
        {
            return new List<ColorScheme>
            {
                // Material Design
                new ColorScheme
                {
                    Name = "Material Design",
                    PaletteType = ColorPalette.Material,
                    Description = "Google's Material Design color system with vibrant, bold colors",
                    Primary = Color.FromRgb(0x67, 0x3A, 0xB7), // Deep Purple
                    PrimaryLight = Color.FromRgb(0x9C, 0x64, 0xE2),
                    PrimaryDark = Color.FromRgb(0x32, 0x0B, 0x86),
                    Secondary = Color.FromRgb(0x03, 0xDA, 0xC6), // Teal
                    Accent = Color.FromRgb(0xFF, 0x57, 0x22), // Deep Orange
                    Success = Color.FromRgb(0x4C, 0xAF, 0x50), // Green
                    Warning = Color.FromRgb(0xFF, 0x98, 0x00), // Orange
                    Error = Color.FromRgb(0xF4, 0x43, 0x36), // Red
                    Info = Color.FromRgb(0x21, 0x96, 0xF3), // Blue
                    Background = Color.FromRgb(0xFA, 0xFA, 0xFA),
                    Surface = Colors.White,
                    Card = Colors.White
                },

                // Flat UI
                new ColorScheme
                {
                    Name = "Flat UI",
                    PaletteType = ColorPalette.FlatUI,
                    Description = "Clean, flat design with muted colors and minimal shadows",
                    Primary = Color.FromRgb(0x34, 0x98, 0xDB), // Peter River
                    Secondary = Color.FromRgb(0x95, 0xA5, 0xA6), // Concrete
                    Accent = Color.FromRgb(0xE7, 0x4C, 0x3C), // Alizarin
                    Success = Color.FromRgb(0x2E, 0xCC, 0x71), // Emerald
                    Warning = Color.FromRgb(0xF3, 0x9C, 0x12), // Orange
                    Error = Color.FromRgb(0xE7, 0x4C, 0x3C), // Alizarin
                    Info = Color.FromRgb(0x34, 0x98, 0xDB), // Peter River
                    Background = Color.FromRgb(0xEC, 0xF0, 0xF1),
                    Surface = Colors.White,
                    Card = Colors.White
                },

                // Bootstrap
                new ColorScheme
                {
                    Name = "Bootstrap",
                    PaletteType = ColorPalette.Bootstrap,
                    Description = "Bootstrap's default color scheme with professional, accessible colors",
                    Primary = Color.FromRgb(0x00, 0x7B, 0xFF), // Blue
                    Secondary = Color.FromRgb(0x6C, 0x75, 0x7D), // Gray
                    Accent = Color.FromRgb(0x17, 0xA2, 0xB8), // Info
                    Success = Color.FromRgb(0x28, 0xA7, 0x45), // Green
                    Warning = Color.FromRgb(0xFF, 0xC1, 0x07), // Yellow
                    Error = Color.FromRgb(0xDC, 0x35, 0x45), // Red
                    Info = Color.FromRgb(0x17, 0xA2, 0xB8), // Cyan
                    Background = Colors.White,
                    Surface = Color.FromRgb(0xF8, 0xF9, 0xFA),
                    Card = Colors.White
                },

                // Tailwind CSS
                new ColorScheme
                {
                    Name = "Tailwind CSS",
                    PaletteType = ColorPalette.Tailwind,
                    Description = "Tailwind's utility-first color system with extensive palette",
                    Primary = Color.FromRgb(0x37, 0x84, 0xF6), // Blue-500
                    Secondary = Color.FromRgb(0x6B, 0x72, 0x80), // Gray-500
                    Accent = Color.FromRgb(0x8B, 0x5C, 0xF6), // Purple-500
                    Success = Color.FromRgb(0x10, 0xB9, 0x81), // Emerald-500
                    Warning = Color.FromRgb(0xF5, 0x9E, 0x0B), // Amber-500
                    Error = Color.FromRgb(0xEF, 0x44, 0x44), // Red-500
                    Info = Color.FromRgb(0x06, 0xB6, 0xD4), // Cyan-500
                    Background = Colors.White,
                    Surface = Color.FromRgb(0xF9, 0xFA, 0xFB),
                    Card = Colors.White
                }
            };
        }

        private List<ColorScheme> LoadCustomColorSchemes()
        {
            var schemes = new List<ColorScheme>();
            
            try
            {
                var files = Directory.GetFiles(_customSchemesPath, "*.json");
                foreach (var file in files)
                {
                    var json = File.ReadAllText(file);
                    var scheme = JsonConvert.DeserializeObject<ColorScheme>(json);
                    if (scheme != null)
                    {
                        schemes.Add(scheme);
                    }
                }
            }
            catch
            {
                // Handle errors silently
            }
            
            return schemes;
        }

        private ColorScheme GetDefaultColorScheme()
        {
            return _predefinedSchemes.First(s => s.PaletteType == ColorPalette.Material);
        }

        private Dictionary<string, string> GenerateCssVariablesDict(ColorScheme scheme)
        {
            return new Dictionary<string, string>
            {
                { "color-primary", ColorToHex(scheme.Primary) },
                { "color-primary-light", ColorToHex(scheme.PrimaryLight) },
                { "color-primary-dark", ColorToHex(scheme.PrimaryDark) },
                { "color-secondary", ColorToHex(scheme.Secondary) },
                { "color-accent", ColorToHex(scheme.Accent) },
                { "color-success", ColorToHex(scheme.Success) },
                { "color-warning", ColorToHex(scheme.Warning) },
                { "color-error", ColorToHex(scheme.Error) },
                { "color-info", ColorToHex(scheme.Info) },
                { "color-background", ColorToHex(scheme.Background) },
                { "color-surface", ColorToHex(scheme.Surface) },
                { "color-card", ColorToHex(scheme.Card) }
            };
        }

        private string ColorToHex(Color color)
        {
            return $"#{color.R:X2}{color.G:X2}{color.B:X2}";
        }

        private double CalculateContrastRatio(Color foreground, Color background)
        {
            var l1 = GetRelativeLuminance(foreground);
            var l2 = GetRelativeLuminance(background);
            
            var lighter = Math.Max(l1, l2);
            var darker = Math.Min(l1, l2);
            
            return (lighter + 0.05) / (darker + 0.05);
        }

        private double GetRelativeLuminance(Color color)
        {
            var r = color.R / 255.0;
            var g = color.G / 255.0;
            var b = color.B / 255.0;

            r = r <= 0.03928 ? r / 12.92 : Math.Pow((r + 0.055) / 1.055, 2.4);
            g = g <= 0.03928 ? g / 12.92 : Math.Pow((g + 0.055) / 1.055, 2.4);
            b = b <= 0.03928 ? b / 12.92 : Math.Pow((b + 0.055) / 1.055, 2.4);

            return 0.2126 * r + 0.7152 * g + 0.0722 * b;
        }

        private AccessibilityLevel GetAccessibilityLevel(double contrastRatio)
        {
            if (contrastRatio >= 7.0) return AccessibilityLevel.AAA;
            if (contrastRatio >= 4.5) return AccessibilityLevel.AA;
            return AccessibilityLevel.Failed;
        }

        private string GetAccessibilityRecommendation(double contrastRatio)
        {
            return contrastRatio switch
            {
                >= 7.0 => "Excellent contrast - meets AAA standards",
                >= 4.5 => "Good contrast - meets AA standards",
                >= 3.0 => "Poor contrast - consider adjusting colors",
                _ => "Very poor contrast - colors must be changed"
            };
        }

        private ColorCategory GetSchemeCategory(ColorScheme scheme)
        {
            // Simple categorization logic
            return scheme.PaletteType switch
            {
                ColorPalette.Material => ColorCategory.Modern,
                ColorPalette.FlatUI => ColorCategory.Minimal,
                ColorPalette.Bootstrap => ColorCategory.Classic,
                ColorPalette.Tailwind => ColorCategory.Modern,
                _ => ColorCategory.Classic
            };
        }

        private string GenerateTailwindConfig(ColorScheme scheme)
        {
            return $@"module.exports = {{
  theme: {{
    extend: {{
      colors: {{
        primary: '{ColorToHex(scheme.Primary)}',
        secondary: '{ColorToHex(scheme.Secondary)}',
        accent: '{ColorToHex(scheme.Accent)}',
        success: '{ColorToHex(scheme.Success)}',
        warning: '{ColorToHex(scheme.Warning)}',
        error: '{ColorToHex(scheme.Error)}',
      }}
    }}
  }}
}}";
        }

        private string GenerateBootstrapVariables(ColorScheme scheme)
        {
            return $@"$primary: {ColorToHex(scheme.Primary)};
$secondary: {ColorToHex(scheme.Secondary)};
$success: {ColorToHex(scheme.Success)};
$warning: {ColorToHex(scheme.Warning)};
$danger: {ColorToHex(scheme.Error)};
$info: {ColorToHex(scheme.Info)};";
        }

        private string GenerateMaterialTokens(ColorScheme scheme)
        {
            return $@"{{
  ""color"": {{
    ""primary"": ""{ColorToHex(scheme.Primary)}"",
    ""secondary"": ""{ColorToHex(scheme.Secondary)}"",
    ""surface"": ""{ColorToHex(scheme.Surface)}"",
    ""background"": ""{ColorToHex(scheme.Background)}"",
    ""error"": ""{ColorToHex(scheme.Error)}""
  }}
}}";
        }

        private string GenerateAntDesignTokens(ColorScheme scheme)
        {
            return $@"{{
  ""@primary-color"": ""{ColorToHex(scheme.Primary)}"",
  ""@success-color"": ""{ColorToHex(scheme.Success)}"",
  ""@warning-color"": ""{ColorToHex(scheme.Warning)}"",
  ""@error-color"": ""{ColorToHex(scheme.Error)}"",
  ""@info-color"": ""{ColorToHex(scheme.Info)}""
}}";
        }

        #endregion
    }
}
