C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\PromptBuilderAI.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\MainWindow.g.cs
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\Views\SettingsWindow.g.cs
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\App.g.cs
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\GeneratedInternalTypeHelper.g.cs
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\PromptBuilderAI_MarkupCompile.cache
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\PromptBuilderAI_MarkupCompile.lref
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\PromptBuilderAI.exe
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\PromptBuilderAI.deps.json
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\PromptBuilderAI.runtimeconfig.json
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\PromptBuilderAI.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\PromptBuilderAI.pdb
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\CommunityToolkit.Mvvm.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\LiteDB.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\MaterialDesignColors.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\MaterialDesignThemes.Wpf.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Binder.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\Microsoft.Extensions.Diagnostics.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\Microsoft.Extensions.Diagnostics.Abstractions.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\Microsoft.Extensions.Http.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\Microsoft.Extensions.Options.ConfigurationExtensions.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\Microsoft.Xaml.Behaviors.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\Newtonsoft.Json.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\bin\Debug\net8.0-windows\OpenAI_API.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\App.baml
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\MainWindow.baml
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\Views\SettingsWindow.baml
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\PromptBuilderAI.g.resources
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\PromptBuilderAI.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\PromptBuilderAI.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\PromptBu.********.Up2Date
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\PromptBuilderAI.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\refint\PromptBuilderAI.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\PromptBuilderAI.pdb
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\PromptBuilderAI.genruntimeconfig.cache
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\ref\PromptBuilderAI.dll
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\Licensing\Views\ActivationWindow.baml
C:\Users\<USER>\Desktop\PromptBuilderAI\PromptBuilderAI\obj\Debug\net8.0-windows\Licensing\Views\ActivationWindow.g.cs
