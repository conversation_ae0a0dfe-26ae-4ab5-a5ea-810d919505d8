using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PromptBuilderAI.Models;

namespace PromptBuilderAI.Services
{
	public interface IOpenRouterService
	{
		Task<string> EnhancePromptAsync(string originalPrompt, string apiKey, string model);
		Task<List<OpenRouterModel>> GetAvailableModelsAsync(string apiKey);
		Task<List<OpenRouterModel>> SearchModelsAsync(string apiKey, string query);
		List<string> GetDefaultModels();

		// New validation methods
		Task<ApiValidationResult> ValidateApiKeyAsync(string apiKey);
		Task<ModelValidationResult> ValidateModelAsync(string apiKey, string modelId);
		Task<ConnectionTestResult> TestFullConnectionAsync(string apiKey, string modelId);
	}

	// Validation result classes
	public class ApiValidationResult
	{
		public bool IsValid { get; set; }
		public string Message { get; set; } = string.Empty;
		public string ErrorCode { get; set; } = string.Empty;
		public int AvailableModelsCount { get; set; }
		public decimal? AccountBalance { get; set; }
		public string AccountType { get; set; } = string.Empty;
	}

	public class ModelValidationResult
	{
		public bool IsValid { get; set; }
		public bool IsAvailable { get; set; }
		public string Message { get; set; } = string.Empty;
		public string ModelName { get; set; } = string.Empty;
		public int ContextLength { get; set; }
		public string PricingInfo { get; set; } = string.Empty;
	}

	public class ConnectionTestResult
	{
		public bool IsSuccessful { get; set; }
		public string Message { get; set; } = string.Empty;
		public ApiValidationResult ApiResult { get; set; } = new();
		public ModelValidationResult ModelResult { get; set; } = new();
		public long ResponseTimeMs { get; set; }
		public string TestResponse { get; set; } = string.Empty;
	}
}
