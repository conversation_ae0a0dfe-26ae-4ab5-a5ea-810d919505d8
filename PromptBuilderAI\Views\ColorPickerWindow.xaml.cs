using System.Windows;
using PromptBuilderAI.ViewModels;

namespace PromptBuilderAI.Views
{
    /// <summary>
    /// Interaction logic for ColorPickerWindow.xaml
    /// </summary>
    public partial class ColorPickerWindow : Window
    {
        public ColorPickerViewModel ViewModel { get; }

        public ColorPickerWindow()
        {
            InitializeComponent();
            ViewModel = new ColorPickerViewModel();
            DataContext = ViewModel;
        }

        public ColorPickerWindow(string initialName, string initialDescription) : this()
        {
            ViewModel.SchemeName = initialName;
            ViewModel.SchemeDescription = initialDescription;
        }

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            DialogResult = ViewModel.DialogResult;
        }
    }
}
