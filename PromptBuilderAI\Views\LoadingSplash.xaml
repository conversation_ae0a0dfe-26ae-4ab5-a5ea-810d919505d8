<UserControl x:Class="PromptBuilderAI.Views.LoadingSplash"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    
    <UserControl.Resources>
        <!-- Gradient Background -->
        <LinearGradientBrush x:Key="SplashGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#181818" Offset="0"/>
            <GradientStop Color="#2D1B69" Offset="0.5"/>
            <GradientStop Color="#181818" Offset="1"/>
        </LinearGradientBrush>
        
        <!-- Glow Effect -->
        <DropShadowEffect x:Key="GlowEffect" 
                         Color="#673AB7" 
                         BlurRadius="20" 
                         ShadowDepth="0" 
                         Opacity="0.6"/>
    </UserControl.Resources>
    
    <Border CornerRadius="15" 
            Background="{StaticResource SplashGradient}"
            BorderBrush="#673AB7" 
            BorderThickness="2"
            Effect="{StaticResource GlowEffect}">
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Logo/Icon Section -->
            <StackPanel Grid.Row="1" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center"
                       Margin="0,20,0,0">
                
                <!-- App Icon -->
                <materialDesign:PackIcon Kind="RobotExcited" 
                                       Width="80" Height="80"
                                       Foreground="#673AB7"
                                       HorizontalAlignment="Center"
                                       Margin="0,0,0,20"/>
                
                <!-- App Title -->
                <TextBlock Text="Prompt Builder AI"
                          FontSize="32"
                          FontWeight="Bold"
                          Foreground="White"
                          HorizontalAlignment="Center"
                          FontFamily="Segoe UI Light"/>
                
                <!-- Subtitle -->
                <TextBlock Text="Convert your ideas into professional AI prompts"
                          FontSize="14"
                          Foreground="#B0B0B0"
                          HorizontalAlignment="Center"
                          Margin="0,10,0,0"
                          FontStyle="Italic"/>
            </StackPanel>
            
            <!-- Progress Section -->
            <StackPanel Grid.Row="2" 
                       Margin="60,40,60,20"
                       HorizontalAlignment="Stretch">
                
                <!-- Progress Bar -->
                <ProgressBar x:Name="ProgressBar"
                           Height="6"
                           Background="#333333"
                           Foreground="#673AB7"
                           BorderThickness="0"
                           Value="0"
                           Maximum="100"/>
                
                <!-- Loading Text -->
                <TextBlock x:Name="LoadingText"
                          Text="Initializing..."
                          FontSize="12"
                          Foreground="#B0B0B0"
                          HorizontalAlignment="Center"
                          Margin="0,10,0,0"/>
            </StackPanel>
            
            <!-- Version Info -->
            <StackPanel Grid.Row="3" 
                       Orientation="Horizontal"
                       HorizontalAlignment="Center"
                       Margin="0,20,0,0">
                
                <TextBlock Text="Version "
                          FontSize="10"
                          Foreground="#808080"/>
                
                <TextBlock x:Name="VersionText"
                          Text="1.0.0"
                          FontSize="10"
                          Foreground="#673AB7"
                          FontWeight="SemiBold"/>
            </StackPanel>
            
            <!-- Copyright -->
            <TextBlock Grid.Row="4"
                      Text="© 2025 Prompt Builder AI"
                      FontSize="10"
                      Foreground="#606060"
                      HorizontalAlignment="Center"
                      Margin="0,10,0,20"/>
        </Grid>
    </Border>
</UserControl>
