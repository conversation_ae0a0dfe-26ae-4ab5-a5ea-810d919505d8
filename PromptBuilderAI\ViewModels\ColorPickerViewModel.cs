#nullable enable
using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Media;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PromptBuilderAI.Models;
using PromptBuilderAI.Services;

namespace PromptBuilderAI.ViewModels
{
    public partial class ColorPickerViewModel : ObservableObject
    {
        private readonly IColorPaletteService _colorPaletteService;

        [ObservableProperty]
        private string schemeName = "My Custom Scheme";

        [ObservableProperty]
        private string schemeDescription = "A beautiful custom color scheme";

        [ObservableProperty]
        private Color primaryColor = Color.FromRgb(0x67, 0x3A, 0xB7);

        [ObservableProperty]
        private Color secondaryColor = Color.FromRgb(0x03, 0xDA, 0xC6);

        [ObservableProperty]
        private Color accentColor = Color.FromRgb(0xFF, 0x57, 0x22);

        [ObservableProperty]
        private Color successColor = Color.FromRgb(0x4C, 0xAF, 0x50);

        [ObservableProperty]
        private Color warningColor = Color.FromRgb(0xFF, 0x98, 0x00);

        [ObservableProperty]
        private Color errorColor = Color.FromRgb(0xF4, 0x43, 0x36);

        [ObservableProperty]
        private Color backgroundColor = Color.FromRgb(0xFA, 0xFA, 0xFA);

        [ObservableProperty]
        private Color surfaceColor = Colors.White;

        // Hex representations for display
        public string PrimaryColorHex => ColorToHex(PrimaryColor);
        public string SecondaryColorHex => ColorToHex(SecondaryColor);
        public string AccentColorHex => ColorToHex(AccentColor);
        public string SuccessColorHex => ColorToHex(SuccessColor);
        public string WarningColorHex => ColorToHex(WarningColor);
        public string ErrorColorHex => ColorToHex(ErrorColor);
        public string BackgroundColorHex => ColorToHex(BackgroundColor);
        public string SurfaceColorHex => ColorToHex(SurfaceColor);

        public ObservableCollection<ColorPreset> ColorPresets { get; } = new ObservableCollection<ColorPreset>();

        public ColorScheme? ResultColorScheme { get; private set; }
        public bool DialogResult { get; private set; }

        public ColorPickerViewModel()
        {
            _colorPaletteService = new ColorPaletteService();
            InitializeColorPresets();
        }

        [RelayCommand]
        private void SelectPrimaryColor()
        {
            var color = ShowColorDialog(PrimaryColor);
            if (color.HasValue)
            {
                PrimaryColor = color.Value;
                OnPropertyChanged(nameof(PrimaryColorHex));
            }
        }

        [RelayCommand]
        private void SelectSecondaryColor()
        {
            var color = ShowColorDialog(SecondaryColor);
            if (color.HasValue)
            {
                SecondaryColor = color.Value;
                OnPropertyChanged(nameof(SecondaryColorHex));
            }
        }

        [RelayCommand]
        private void SelectAccentColor()
        {
            var color = ShowColorDialog(AccentColor);
            if (color.HasValue)
            {
                AccentColor = color.Value;
                OnPropertyChanged(nameof(AccentColorHex));
            }
        }

        [RelayCommand]
        private void SelectSuccessColor()
        {
            var color = ShowColorDialog(SuccessColor);
            if (color.HasValue)
            {
                SuccessColor = color.Value;
                OnPropertyChanged(nameof(SuccessColorHex));
            }
        }

        [RelayCommand]
        private void SelectWarningColor()
        {
            var color = ShowColorDialog(WarningColor);
            if (color.HasValue)
            {
                WarningColor = color.Value;
                OnPropertyChanged(nameof(WarningColorHex));
            }
        }

        [RelayCommand]
        private void SelectErrorColor()
        {
            var color = ShowColorDialog(ErrorColor);
            if (color.HasValue)
            {
                ErrorColor = color.Value;
                OnPropertyChanged(nameof(ErrorColorHex));
            }
        }

        [RelayCommand]
        private void SelectBackgroundColor()
        {
            var color = ShowColorDialog(BackgroundColor);
            if (color.HasValue)
            {
                BackgroundColor = color.Value;
                OnPropertyChanged(nameof(BackgroundColorHex));
            }
        }

        [RelayCommand]
        private void SelectSurfaceColor()
        {
            var color = ShowColorDialog(SurfaceColor);
            if (color.HasValue)
            {
                SurfaceColor = color.Value;
                OnPropertyChanged(nameof(SurfaceColorHex));
            }
        }

        [RelayCommand]
        private void ApplyPreset(ColorPreset preset)
        {
            if (preset == null) return;

            PrimaryColor = preset.PrimaryColor;
            SecondaryColor = preset.SecondaryColor;
            AccentColor = preset.AccentColor;
            SuccessColor = preset.SuccessColor;
            WarningColor = preset.WarningColor;
            ErrorColor = preset.ErrorColor;
            BackgroundColor = preset.BackgroundColor;
            SurfaceColor = preset.SurfaceColor;

            // Update hex displays
            OnPropertyChanged(nameof(PrimaryColorHex));
            OnPropertyChanged(nameof(SecondaryColorHex));
            OnPropertyChanged(nameof(AccentColorHex));
            OnPropertyChanged(nameof(SuccessColorHex));
            OnPropertyChanged(nameof(WarningColorHex));
            OnPropertyChanged(nameof(ErrorColorHex));
            OnPropertyChanged(nameof(BackgroundColorHex));
            OnPropertyChanged(nameof(SurfaceColorHex));

            SchemeName = preset.Name;
            SchemeDescription = preset.Description;
        }

        [RelayCommand]
        private void SaveScheme()
        {
            if (string.IsNullOrWhiteSpace(SchemeName))
            {
                MessageBox.Show("Please enter a name for your color scheme.", "Name Required",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                ResultColorScheme = new ColorScheme
                {
                    Name = SchemeName,
                    Description = SchemeDescription,
                    PaletteType = ColorPalette.Custom,
                    Primary = PrimaryColor,
                    Secondary = SecondaryColor,
                    Accent = AccentColor,
                    Success = SuccessColor,
                    Warning = WarningColor,
                    Error = ErrorColor,
                    Background = BackgroundColor,
                    Surface = SurfaceColor
                };

                // Generate CSS variables
                ResultColorScheme.CssVariables = GenerateCssVariables();

                DialogResult = true;
                CloseWindow();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error creating color scheme: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            DialogResult = false;
            CloseWindow();
        }

        private Color? ShowColorDialog(Color currentColor)
        {
            try
            {
                // Use Windows Forms ColorDialog for now
                // In a real implementation, you might want to use a more advanced color picker
                var colorDialog = new System.Windows.Forms.ColorDialog
                {
                    Color = System.Drawing.Color.FromArgb(currentColor.R, currentColor.G, currentColor.B),
                    FullOpen = true,
                    AllowFullOpen = true
                };

                if (colorDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    var selectedColor = colorDialog.Color;
                    return Color.FromRgb(selectedColor.R, selectedColor.G, selectedColor.B);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening color dialog: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }

            return null;
        }

        private void InitializeColorPresets()
        {
            ColorPresets.Add(new ColorPreset
            {
                Name = "Ocean Blue",
                Description = "Cool ocean-inspired colors",
                PrimaryColor = Color.FromRgb(0x00, 0x7B, 0xFF),
                SecondaryColor = Color.FromRgb(0x00, 0xBF, 0xFF),
                AccentColor = Color.FromRgb(0xFF, 0x6B, 0x35),
                SuccessColor = Color.FromRgb(0x00, 0xE6, 0x76),
                WarningColor = Color.FromRgb(0xFF, 0xD5, 0x4F),
                ErrorColor = Color.FromRgb(0xFF, 0x52, 0x52),
                BackgroundColor = Color.FromRgb(0xF8, 0xFA, 0xFF),
                SurfaceColor = Colors.White
            });

            ColorPresets.Add(new ColorPreset
            {
                Name = "Sunset Vibes",
                Description = "Warm sunset colors",
                PrimaryColor = Color.FromRgb(0xFF, 0x6B, 0x35),
                SecondaryColor = Color.FromRgb(0xFF, 0x9F, 0x43),
                AccentColor = Color.FromRgb(0xFF, 0xD2, 0x3F),
                SuccessColor = Color.FromRgb(0x4C, 0xAF, 0x50),
                WarningColor = Color.FromRgb(0xFF, 0x98, 0x00),
                ErrorColor = Color.FromRgb(0xF4, 0x43, 0x36),
                BackgroundColor = Color.FromRgb(0xFF, 0xF8, 0xF0),
                SurfaceColor = Color.FromRgb(0xFF, 0xFF, 0xFF)
            });

            ColorPresets.Add(new ColorPreset
            {
                Name = "Forest Green",
                Description = "Natural forest colors",
                PrimaryColor = Color.FromRgb(0x2E, 0x7D, 0x32),
                SecondaryColor = Color.FromRgb(0x66, 0xBB, 0x6A),
                AccentColor = Color.FromRgb(0xFF, 0xB7, 0x4D),
                SuccessColor = Color.FromRgb(0x4C, 0xAF, 0x50),
                WarningColor = Color.FromRgb(0xFF, 0x98, 0x00),
                ErrorColor = Color.FromRgb(0xF4, 0x43, 0x36),
                BackgroundColor = Color.FromRgb(0xF1, 0xF8, 0xE9),
                SurfaceColor = Colors.White
            });

            ColorPresets.Add(new ColorPreset
            {
                Name = "Purple Dreams",
                Description = "Elegant purple theme",
                PrimaryColor = Color.FromRgb(0x67, 0x3A, 0xB7),
                SecondaryColor = Color.FromRgb(0x9C, 0x27, 0xB0),
                AccentColor = Color.FromRgb(0xFF, 0x57, 0x22),
                SuccessColor = Color.FromRgb(0x4C, 0xAF, 0x50),
                WarningColor = Color.FromRgb(0xFF, 0x98, 0x00),
                ErrorColor = Color.FromRgb(0xF4, 0x43, 0x36),
                BackgroundColor = Color.FromRgb(0xF3, 0xE5, 0xF5),
                SurfaceColor = Colors.White
            });

            ColorPresets.Add(new ColorPreset
            {
                Name = "Dark Mode",
                Description = "Modern dark theme",
                PrimaryColor = Color.FromRgb(0xBB, 0x86, 0xFC),
                SecondaryColor = Color.FromRgb(0x03, 0xDA, 0xC6),
                AccentColor = Color.FromRgb(0xFF, 0x57, 0x22),
                SuccessColor = Color.FromRgb(0x4C, 0xAF, 0x50),
                WarningColor = Color.FromRgb(0xFF, 0xB7, 0x4D),
                ErrorColor = Color.FromRgb(0xF4, 0x43, 0x36),
                BackgroundColor = Color.FromRgb(0x12, 0x12, 0x12),
                SurfaceColor = Color.FromRgb(0x1E, 0x1E, 0x1E)
            });

            ColorPresets.Add(new ColorPreset
            {
                Name = "Minimalist",
                Description = "Clean minimal colors",
                PrimaryColor = Color.FromRgb(0x21, 0x21, 0x21),
                SecondaryColor = Color.FromRgb(0x75, 0x75, 0x75),
                AccentColor = Color.FromRgb(0x00, 0x7B, 0xFF),
                SuccessColor = Color.FromRgb(0x4C, 0xAF, 0x50),
                WarningColor = Color.FromRgb(0xFF, 0x98, 0x00),
                ErrorColor = Color.FromRgb(0xF4, 0x43, 0x36),
                BackgroundColor = Color.FromRgb(0xFA, 0xFA, 0xFA),
                SurfaceColor = Colors.White
            });
        }

        private System.Collections.Generic.Dictionary<string, string> GenerateCssVariables()
        {
            return new System.Collections.Generic.Dictionary<string, string>
            {
                { "color-primary", ColorToHex(PrimaryColor) },
                { "color-secondary", ColorToHex(SecondaryColor) },
                { "color-accent", ColorToHex(AccentColor) },
                { "color-success", ColorToHex(SuccessColor) },
                { "color-warning", ColorToHex(WarningColor) },
                { "color-error", ColorToHex(ErrorColor) },
                { "color-background", ColorToHex(BackgroundColor) },
                { "color-surface", ColorToHex(SurfaceColor) }
            };
        }

        private string ColorToHex(Color color)
        {
            return $"#{color.R:X2}{color.G:X2}{color.B:X2}";
        }

        private void CloseWindow()
        {
            // This will be handled by the code-behind
            Application.Current.Windows[Application.Current.Windows.Count - 1]?.Close();
        }
    }

    /// <summary>
    /// Represents a color preset for quick selection
    /// </summary>
    public class ColorPreset
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public Color PrimaryColor { get; set; }
        public Color SecondaryColor { get; set; }
        public Color AccentColor { get; set; }
        public Color SuccessColor { get; set; }
        public Color WarningColor { get; set; }
        public Color ErrorColor { get; set; }
        public Color BackgroundColor { get; set; }
        public Color SurfaceColor { get; set; }
    }
}
